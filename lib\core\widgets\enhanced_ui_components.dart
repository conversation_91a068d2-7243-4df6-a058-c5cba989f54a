import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../theme/index.dart';

/// مكونات واجهة المستخدم المحسنة باستخدام Material Design 3 والنظام الذكي للثيمات
/// 🎨 مكونات ذكية تتكيف مع الثيم الحالي مع ألوان عصرية
/// 🌈 تدرجات لونية متطورة وجميلة مع Material Design 3
/// ♿ ضمان إمكانية الوصول والتباين المناسب WCAG 2.1
/// 🎭 انتقالات سلسة وحركات جميلة مع تفاعلية محسنة
/// 🔥 تحسينات الأداء والتفاعل الحديث
class EnhancedUIComponents {
  EnhancedUIComponents._();

  // ========== البطاقات المحسنة ==========

  /// بطاقة ذكية مع تدرج لوني وظلال متطورة
  static Widget intelligentCard({
    required Widget child,
    required BuildContext context,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? elevation,
    Color? customColor,
    bool useGradient = true,
    bool enableHoverEffect = true,
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: margin ?? const EdgeInsets.all(8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          onTap: enableHoverEffect ? () {} : null,
          child: Container(
            padding: padding ?? AppDimensions.cardPadding,
            decoration: BoxDecoration(
              color: useGradient
                  ? null
                  : (isDark
                      ? primaryColor.withValues(alpha: 0.1)
                      : primaryColor.withValues(alpha: 0.05)),
              gradient: useGradient
                  ? LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        primaryColor.withValues(alpha: 0.1),
                        primaryColor.withValues(alpha: 0.05),
                      ],
                    )
                  : null,
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              border: Border.all(
                color: primaryColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: child,
          ),
        ),
      ),
    );
  }

  /// بطاقة إحصائيات متطورة مع Material Design 3 وتفاعلية محسنة وألوان حيوية
  static Widget statsCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required BuildContext context,
    Color? customColor,
    VoidCallback? onTap,
    bool showTrend = false,
    double? trendValue,
    bool isPositiveTrend = true,
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;
    final isDark = theme.brightness == Brightness.dark;

    return SizedBox(
      height: 140,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          splashColor: AppColors.primaryHover(primaryColor),
          highlightColor: AppColors.primaryFocused(primaryColor),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  primaryColor,
                  primaryColor.darken(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withValues(alpha: isDark ? 0.4 : 0.25),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: primaryColor.withValues(alpha: isDark ? 0.2 : 0.1),
                  blurRadius: 24,
                  offset: const Offset(0, 12),
                  spreadRadius: 0,
                ),
              ],
            ),
            padding: AppDimensions.cardPaddingMedium,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // الصف العلوي مع تحسينات بصرية
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      // أيقونة دائرية احترافية مثل الإجراءات السريعة
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.white
                              .withValues(alpha: isDark ? 0.2 : 0.1),
                          shape:
                              BoxShape.circle, // دائرية مثل الإجراءات السريعة
                          border: Border.all(
                            color: AppColors.white.withValues(alpha: 0.1),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          icon,
                          color: AppColors.white,
                          size: 28, // نفس حجم أيقونات الإجراءات السريعة
                        ),
                      ),
                    ),
                    if (showTrend && trendValue != null)
                      Flexible(
                        child: _buildEnhancedTrendIndicator(
                            trendValue, isPositiveTrend),
                      ),
                  ],
                ),

                // الصف السفلي مع تحسينات الطباعة
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // نص القيمة بتصميم احترافي مثل الإجراءات السريعة
                    Text(
                      value,
                      style:
                          AppTypography.lightTextTheme.headlineMedium?.copyWith(
                        color: AppColors.white,
                        fontWeight:
                            FontWeight.w600, // نفس وزن خط الإجراءات السريعة
                        letterSpacing: -0.5,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 6),
                    // نص الوصف بتصميم احترافي
                    Text(
                      subtitle,
                      style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
                        color: AppColors.white.withValues(alpha: 0.85),
                        fontWeight: FontWeight.w500, // وزن خط متوسط احترافي
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // ========== الأزرار المحسنة مع Material Design 3 ==========

  /// زر ذكي مع تدرج لوني وتأثيرات بصرية محسنة
  static Widget intelligentButton({
    required String text,
    required VoidCallback onPressed,
    required BuildContext context,
    IconData? icon,
    Color? customColor,
    ButtonStyle buttonStyle = ButtonStyle.elevated,
    ButtonSize size = ButtonSize.medium,
    bool isLoading = false,
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;
    final isDark = theme.brightness == Brightness.dark;

    Widget buttonChild = isLoading
        ? SizedBox(
            width: _getIconSize(size),
            height: _getIconSize(size),
            child: CircularProgressIndicator(
              strokeWidth: 2.5,
              valueColor: AlwaysStoppedAnimation<Color>(
                buttonStyle == ButtonStyle.elevated
                    ? AppColors.white
                    : primaryColor,
              ),
            ),
          )
        : Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(icon, size: _getIconSize(size)),
                SizedBox(width: size == ButtonSize.small ? 6 : 8),
              ],
              Text(text),
            ],
          );

    switch (buttonStyle) {
      case ButtonStyle.elevated:
        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: primaryColor,
            foregroundColor: AppColors.getTextColorForBackground(primaryColor),
            padding: _getButtonPadding(size),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            ),
            elevation: isDark ? 6 : 4,
            shadowColor: primaryColor.withValues(alpha: isDark ? 0.4 : 0.3),
          ),
          child: buttonChild,
        );

      case ButtonStyle.outlined:
        return OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: OutlinedButton.styleFrom(
            foregroundColor: primaryColor,
            side: BorderSide(color: primaryColor, width: 1.5),
            padding: _getButtonPadding(size),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            ),
          ),
          child: buttonChild,
        );

      case ButtonStyle.text:
        return TextButton(
          onPressed: isLoading ? null : onPressed,
          style: TextButton.styleFrom(
            foregroundColor: primaryColor,
            padding: _getButtonPadding(size),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
            ),
          ),
          child: buttonChild,
        );
    }
  }

  /// زر عائم ذكي
  static Widget intelligentFloatingActionButton({
    required VoidCallback onPressed,
    required IconData icon,
    required BuildContext context,
    Color? customColor,
    String? tooltip,
    bool mini = false,
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;

    return FloatingActionButton(
      onPressed: onPressed,
      backgroundColor: primaryColor,
      foregroundColor: AppColors.white,
      tooltip: tooltip,
      mini: mini,
      elevation: 6,
      child: Icon(icon),
    );
  }

  // ========== حقول الإدخال المحسنة مع Material Design 3 ==========

  /// حقل إدخال ذكي مع تصميم متطور ووصولية محسنة
  static Widget intelligentTextField({
    required String label,
    required BuildContext context,
    TextEditingController? controller,
    String? hintText,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixIconTap,
    bool obscureText = false,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    Color? customColor,
    bool enabled = true,
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;
    final isDark = theme.brightness == Brightness.dark;

    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType,
      validator: validator,
      enabled: enabled,
      style: theme.textTheme.bodyLarge?.copyWith(
        color: isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
      ),
      decoration: InputDecoration(
        labelText: label,
        hintText: hintText,
        prefixIcon: prefixIcon != null
            ? Icon(
                prefixIcon,
                color: enabled ? primaryColor : AppColors.lightTextDisabled,
                size: AppDimensions.iconSizeMedium,
              )
            : null,
        suffixIcon: suffixIcon != null
            ? IconButton(
                icon: Icon(
                  suffixIcon,
                  color: enabled ? primaryColor : AppColors.lightTextDisabled,
                  size: AppDimensions.iconSizeMedium,
                ),
                onPressed: enabled ? onSuffixIconTap : null,
              )
            : null,
        filled: true,
        fillColor: isDark
            ? AppColors.darkSurfaceVariant.withValues(alpha: 0.3)
            : AppColors.lightSurfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
            width: 1.5,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.lightBorder,
            width: 1.5,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(color: primaryColor, width: 2.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: const BorderSide(color: AppColors.error, width: 2.5),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: const BorderSide(color: AppColors.error, width: 2.5),
        ),
        labelStyle: TextStyle(
          color: enabled ? primaryColor : AppColors.lightTextDisabled,
          fontWeight: AppTypography.weightMedium,
        ),
        hintStyle: TextStyle(
          color: isDark ? AppColors.darkTextHint : AppColors.lightTextHint,
          fontWeight: AppTypography.weightRegular,
        ),
        contentPadding: AppDimensions.itemPaddingLarge,
      ),
    );
  }

  // ========== دوال مساعدة خاصة محسنة ==========

  /// مؤشر الاتجاه المحسن مع Material Design 3
  static Widget _buildEnhancedTrendIndicator(
      double trendValue, bool isPositive) {
    return Flexible(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.white.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
          border: Border.all(
            color: AppColors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isPositive
                  ? Icons.trending_up_rounded
                  : Icons.trending_down_rounded,
              color: AppColors.white,
              size: 16,
            ),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                '${trendValue.abs().toStringAsFixed(1)}%',
                style: AppTypography.lightTextTheme.bodySmall?.copyWith(
                  color: AppColors.white,
                  fontWeight: AppTypography.weightBold,
                  fontSize: 11,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  static EdgeInsetsGeometry _getButtonPadding(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
      case ButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 32, vertical: 16);
    }
  }

  static double _getIconSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 20;
      case ButtonSize.large:
        return 24;
    }
  }

  // ========== مكونات مبتكرة جديدة للداش بورد ==========

  /// بطاقة إحصائيات متحركة مع تأثيرات بصرية مذهلة
  static Widget animatedStatsCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required BuildContext context,
    Color? customColor,
    VoidCallback? onTap,
    bool showTrend = false,
    double? trendValue,
    bool isPositiveTrend = true,
    bool showSparkline = false,
    List<double>? sparklineData,
    Duration animationDuration = const Duration(milliseconds: 800),
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;
    final isDark = theme.brightness == Brightness.dark;

    return TweenAnimationBuilder<double>(
      duration: animationDuration,
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.elasticOut,
      builder: (context, animation, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * animation),
          child: Opacity(
            opacity: animation,
            child: Container(
              height: 160,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    primaryColor,
                    primaryColor.darken(0.15),
                    primaryColor.darken(0.25),
                  ],
                  stops: const [0.0, 0.6, 1.0],
                ),
                borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: isDark ? 0.4 : 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: primaryColor.withValues(alpha: isDark ? 0.2 : 0.15),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Material(
                color: AppColors.transparent,
                child: InkWell(
                  onTap: onTap,
                  borderRadius:
                      BorderRadius.circular(AppDimensions.radiusLarge),
                  child: Padding(
                    padding: AppDimensions.cardPaddingLarge,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // الصف العلوي مع الأيقونة والاتجاه
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // أيقونة دائرية احترافية مثل الإجراءات السريعة
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: AppColors.white
                                    .withValues(alpha: isDark ? 0.2 : 0.1),
                                shape: BoxShape
                                    .circle, // دائرية مثل الإجراءات السريعة
                                border: Border.all(
                                  color: AppColors.white.withValues(alpha: 0.1),
                                  width: 1,
                                ),
                              ),
                              child: Icon(
                                icon,
                                color: AppColors.white,
                                size: 28, // نفس حجم أيقونات الإجراءات السريعة
                              ),
                            ),
                            if (showTrend && trendValue != null)
                              _buildEnhancedTrendIndicator(
                                  trendValue, isPositiveTrend),
                          ],
                        ),

                        const Spacer(),

                        // القيمة والعنوان بتصميم احترافي مثل الإجراءات السريعة
                        AnimatedDefaultTextStyle(
                          duration: const Duration(milliseconds: 300),
                          style: AppTypography.lightTextTheme.headlineLarge
                                  ?.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight
                                    .w600, // نفس وزن خط الإجراءات السريعة
                                letterSpacing: -1.0,
                              ) ??
                              const TextStyle(),
                          child: Text(
                            value,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        const SizedBox(height: 6),

                        // نص الوصف بتصميم احترافي
                        Text(
                          subtitle,
                          style:
                              AppTypography.lightTextTheme.bodyMedium?.copyWith(
                            color: AppColors.white.withValues(alpha: 0.85),
                            fontWeight: FontWeight.w500, // وزن خط متوسط احترافي
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        // خط الرسم البياني الصغير (اختياري)
                        if (showSparkline &&
                            sparklineData != null &&
                            sparklineData.isNotEmpty)
                          Container(
                            margin: const EdgeInsets.only(top: 8),
                            height: 20,
                            child: CustomPaint(
                              painter: SparklinePainter(
                                data: sparklineData,
                                color: AppColors.white.withValues(alpha: 0.8),
                                strokeWidth: 2,
                              ),
                              size: const Size(double.infinity, 20),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بطاقة وصول سريع مع تأثيرات حديثة
  static Widget modernQuickAccessCard({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
    required BuildContext context,
    Color? customColor,
    String? badge,
    bool isNew = false,
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: isDark ? AppColors.darkSurface : AppColors.lightSurface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
        border: Border.all(
          color: primaryColor.withValues(alpha: 0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: AppColors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          splashColor: primaryColor.withValues(alpha: 0.1),
          highlightColor: primaryColor.withValues(alpha: 0.05),
          child: Stack(
            children: [
              // المحتوى الرئيسي
              Padding(
                padding: AppDimensions.cardPaddingMedium,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: primaryColor.withValues(alpha: 0.1),
                        borderRadius:
                            BorderRadius.circular(AppDimensions.radiusMedium),
                      ),
                      child: Icon(
                        icon,
                        color: primaryColor,
                        size: 32,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      title,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: AppTypography.weightSemiBold,
                        color: isDark
                            ? AppColors.darkTextPrimary
                            : AppColors.lightTextPrimary,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // شارة جديد
              if (isNew)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppColors.error,
                      borderRadius:
                          BorderRadius.circular(AppDimensions.radiusSmall),
                    ),
                    child: Text(
                      'جديد',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: AppTypography.weightBold,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ),

              // شارة مخصصة
              if (badge != null && badge.isNotEmpty)
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.warning,
                      borderRadius:
                          BorderRadius.circular(AppDimensions.radiusSmall),
                    ),
                    child: Text(
                      badge,
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: AppTypography.weightBold,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// بطاقة إحصائيات تفاعلية مع تأثيرات متقدمة
  static Widget interactiveStatsCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required BuildContext context,
    Color? customColor,
    VoidCallback? onTap,
    bool showProgress = false,
    double? progressValue,
    bool showComparison = false,
    String? comparisonText,
    double? comparisonValue,
    bool isComparisonPositive = true,
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      height: 180,
      decoration: BoxDecoration(
        color: isDark ? AppColors.darkSurface : AppColors.lightSurface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
        border: Border.all(
          color: primaryColor.withValues(alpha: 0.2),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          child: Padding(
            padding: AppDimensions.cardPaddingLarge,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الصف العلوي
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            primaryColor.withValues(alpha: 0.2),
                            primaryColor.withValues(alpha: 0.1),
                          ],
                        ),
                        borderRadius:
                            BorderRadius.circular(AppDimensions.radiusMedium),
                      ),
                      child: Icon(
                        icon,
                        color: primaryColor,
                        size: 28,
                      ),
                    ),
                    if (showComparison && comparisonValue != null)
                      Flexible(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 3),
                          decoration: BoxDecoration(
                            color: isComparisonPositive
                                ? AppColors.success.withValues(alpha: 0.1)
                                : AppColors.error.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(
                                AppDimensions.radiusSmall),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                isComparisonPositive
                                    ? Icons.trending_up_rounded
                                    : Icons.trending_down_rounded,
                                color: isComparisonPositive
                                    ? AppColors.success
                                    : AppColors.error,
                                size: 14,
                              ),
                              const SizedBox(width: 3),
                              Flexible(
                                child: Text(
                                  '${comparisonValue.abs().toStringAsFixed(1)}%',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: isComparisonPositive
                                        ? AppColors.success
                                        : AppColors.error,
                                    fontWeight: AppTypography.weightBold,
                                    fontSize: 11,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 16),

                // القيمة الرئيسية
                Text(
                  value,
                  style: theme.textTheme.headlineMedium?.copyWith(
                    color: isDark
                        ? AppColors.darkTextPrimary
                        : AppColors.lightTextPrimary,
                    fontWeight: AppTypography.weightBold,
                    letterSpacing: -0.5,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 4),

                // العنوان الفرعي
                Text(
                  subtitle,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: isDark
                        ? AppColors.darkTextSecondary
                        : AppColors.lightTextSecondary,
                    fontWeight: AppTypography.weightMedium,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                const Spacer(),

                // شريط التقدم (اختياري)
                if (showProgress && progressValue != null) ...[
                  const SizedBox(height: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'التقدم',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: isDark
                                  ? AppColors.darkTextSecondary
                                  : AppColors.lightTextSecondary,
                            ),
                          ),
                          Text(
                            '${(progressValue * 100).toInt()}%',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: primaryColor,
                              fontWeight: AppTypography.weightBold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: LinearProgressIndicator(
                          value: progressValue,
                          backgroundColor: primaryColor.withValues(alpha: 0.2),
                          valueColor:
                              AlwaysStoppedAnimation<Color>(primaryColor),
                          minHeight: 6,
                        ),
                      ),
                    ],
                  ),
                ],

                // نص المقارنة (اختياري)
                if (showComparison && comparisonText != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    comparisonText,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isDark
                          ? AppColors.darkTextSecondary
                          : AppColors.lightTextSecondary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// ========== التعدادات ==========

enum ButtonStyle {
  elevated,
  outlined,
  text,
}

enum ButtonSize {
  small,
  medium,
  large,
}

// ========== كلاسات مساعدة للرسم ==========

/// رسام خط الرسم البياني الصغير
class SparklinePainter extends CustomPainter {
  final List<double> data;
  final Color color;
  final double strokeWidth;

  SparklinePainter({
    required this.data,
    required this.color,
    this.strokeWidth = 2.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty || data.length < 2) return;

    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final path = Path();

    // العثور على القيم الدنيا والعليا
    final minValue = data.reduce((a, b) => a < b ? a : b);
    final maxValue = data.reduce((a, b) => a > b ? a : b);
    final range = maxValue - minValue;

    if (range == 0) return; // تجنب القسمة على صفر

    // حساب النقاط
    for (int i = 0; i < data.length; i++) {
      final x = (i / (data.length - 1)) * size.width;
      final y = size.height - ((data[i] - minValue) / range) * size.height;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! SparklinePainter ||
        oldDelegate.data != data ||
        oldDelegate.color != color ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}

/// رسام الرسم البياني الدائري المبسط
class SimpleDonutChartPainter extends CustomPainter {
  final List<double> values;
  final List<Color> colors;
  final double strokeWidth;

  SimpleDonutChartPainter({
    required this.values,
    required this.colors,
    this.strokeWidth = 12.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (values.isEmpty) return;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 - strokeWidth / 2;
    final total = values.reduce((a, b) => a + b);

    if (total == 0) return;

    double startAngle = -math.pi / 2; // البدء من الأعلى

    for (int i = 0; i < values.length; i++) {
      final sweepAngle = (values[i] / total) * 2 * math.pi;

      final paint = Paint()
        ..color = i < colors.length ? colors[i] : Colors.grey
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth
        ..strokeCap = StrokeCap.round;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        false,
        paint,
      );

      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! SimpleDonutChartPainter ||
        oldDelegate.values != values ||
        oldDelegate.colors != colors ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}
