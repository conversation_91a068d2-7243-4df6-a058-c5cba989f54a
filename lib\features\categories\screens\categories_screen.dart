import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/models/category.dart';
import '../presenters/category_presenter.dart';
import '../../../core/widgets/index.dart'; // استخدام الودجات الموحدة
import 'category_form_screen.dart';
import '../../../core/theme/index.dart';

class CategoriesScreen extends StatefulWidget {
  final String? parentId;
  final String? parentName;
  final String? categoryType; // إضافة نوع الفئة

  const CategoriesScreen({
    Key? key,
    this.parentId,
    this.parentName,
    this.categoryType = 'product', // نوع افتراضي
  }) : super(key: key);

  // قائمة بأنواع الفئات المتاحة
  static const List<Map<String, String>> availableTypes = [
    {'id': 'product', 'name': 'منتجات'},
    {'id': 'customer', 'name': 'عملاء'},
    {'id': 'supplier', 'name': 'موردين'},
    {'id': 'expense', 'name': 'مصروفات'},
    {'id': 'income', 'name': 'إيرادات'},
  ];

  @override
  State<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen> {
  late CategoryPresenter _presenter;

  @override
  void initState() {
    super.initState();
    _presenter = Provider.of<CategoryPresenter>(context, listen: false);
    _loadCategories();
  }

  // تحميل الفئات مع مراعاة النوع
  Future<void> _loadCategories() async {
    await _presenter.loadCategories(type: widget.categoryType);
  }

  // المتغير الحالي لنوع الفئة
  String? _currentType;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_currentType != widget.categoryType) {
      _currentType = widget.categoryType;
      _loadCategories();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UnifiedAppBar(
        title: widget.parentName ?? 'Categories',
        actions: [
          // زر تصفية الفئات حسب النوع
          if (widget.parentId == null) // إظهار فقط في الشاشة الرئيسية
            IconButton(
              icon: const Icon(Icons.filter_list),
              tooltip: 'تصفية حسب النوع',
              onPressed: _showTypeFilterDialog,
            ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToForm(context),
          ),
        ],
      ),
      body: Consumer<CategoryPresenter>(
        builder: (context, presenter, child) {
          if (presenter.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (presenter.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Error: ${presenter.error}',
                    style: const AppTypography(color: AppColors.error),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadCategories,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final categories = widget.parentId != null
              ? presenter.getSubcategories(widget.parentId!)
              : presenter.rootCategories;

          if (categories.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    widget.parentId != null
                        ? 'No subcategories found'
                        : 'No categories found',
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _navigateToForm(context),
                    child: const Text('Add Category'),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return _buildCategoryCard(context, category);
            },
          );
        },
      ),
    );
  }

  Widget _buildCategoryCard(BuildContext context, Category category) {
    final hasSubcategories =
        _presenter.getSubcategories(category.id).isNotEmpty;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: category.imageUrl != null
            ? CircleAvatar(
                backgroundImage: NetworkImage(category.imageUrl!),
              )
            : CircleAvatar(
                child: Text(category.name[0].toUpperCase()),
              ),
        title: Text(
          category.name,
          style: const AppTypography(fontWeight: FontWeight.bold),
        ),
        subtitle: category.description != null
            ? Text(
                category.description!,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              )
            : null,
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (hasSubcategories)
              IconButton(
                icon: const Icon(Icons.chevron_right),
                onPressed: () => _navigateToSubcategories(context, category),
              ),
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _navigateToForm(context, category),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteConfirmation(context, category),
            ),
          ],
        ),
        onTap: hasSubcategories
            ? () => _navigateToSubcategories(context, category)
            : null,
      ),
    );
  }

  void _navigateToSubcategories(BuildContext context, Category category) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CategoriesScreen(
          parentId: category.id,
          parentName: category.name,
          categoryType: widget.categoryType, // نقل نوع الفئة إلى الشاشة الفرعية
        ),
      ),
    );
  }

  Future<void> _navigateToForm(BuildContext context,
      [Category? category]) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CategoryFormScreen(
          category: category,
          parentId: widget.parentId,
          categoryType: widget.categoryType,
        ),
      ),
    );

    if (result == true && mounted) {
      _loadCategories();
    }
  }

  // عرض مربع حوار لتصفية الفئات حسب النوع
  void _showTypeFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية حسب النوع'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              ...CategoriesScreen.availableTypes
                  .map((type) => RadioListTile<String>(
                        title: Text(type['name']!),
                        value: type['id']!,
                        groupValue: _currentType,
                        onChanged: (value) {
                          Navigator.pop(context);
                          if (value != _currentType) {
                            setState(() {
                              _currentType = value;
                            });

                            // إعادة تحميل الفئات بالنوع الجديد
                            _presenter.loadCategories(type: value);
                          }
                        },
                      )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Future<void> _showDeleteConfirmation(
      BuildContext context, Category category) async {
    // حفظ مرجع للسياق قبل العملية غير المتزامنة
    final scaffoldMessengerState = ScaffoldMessenger.of(context);
    final hasSubcategories =
        _presenter.getSubcategories(category.id).isNotEmpty;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to delete ${category.name}?'),
            if (hasSubcategories)
              const Padding(
                padding: EdgeInsets.only(top: 8),
                child: Text(
                  'Warning: This category has subcategories that will also be deleted.',
                  style: AppTypography(color: AppColors.error),
                ),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final success = await _presenter.deleteCategory(category.id);

      if (mounted) {
        scaffoldMessengerState.showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Category deleted successfully'
                  : 'Failed to delete category',
            ),
            backgroundColor: success ? AppColors.success : AppColors.error,
          ),
        );
      }
    }
  }
}
