import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/index.dart';
import '../utils/index.dart';

/// ملف الودجات المشتركة الموحد
/// يحتوي على جميع الودجات الأساسية المستخدمة عبر التطبيق
/// تم توحيدها لتجنب التكرار وضمان التناسق

// ========== شريط التطبيق الموحد ==========

/// شريط التطبيق الموحد والذكي
/// يدعم جميع الخصائص المطلوبة مع التكيف مع الثيم
class UnifiedAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// عنوان الشريط
  final String title;

  /// الإجراءات في الشريط
  final List<Widget>? actions;

  /// هل يتم عرض زر الرجوع
  final bool showBackButton;

  /// دالة عند الضغط على زر القائمة
  final VoidCallback? onMenuPressed;

  /// دالة عند الضغط على زر الرجوع
  final VoidCallback? onBackPressed;

  /// ودجت مخصص في بداية الشريط
  final Widget? leading;

  /// ارتفاع الظل
  final double elevation;

  /// لون الخلفية المخصص
  final Color? backgroundColor;

  /// لون النص المخصص
  final Color? foregroundColor;

  /// ودجت في أسفل الشريط
  final PreferredSizeWidget? bottom;

  /// هل يتم توسيط العنوان
  final bool centerTitle;

  /// ارتفاع الشريط المخصص
  final double? toolbarHeight;

  const UnifiedAppBar({
    super.key,
    required this.title,
    this.actions,
    this.showBackButton = true,
    this.onMenuPressed,
    this.onBackPressed,
    this.leading,
    this.elevation = 0,
    this.backgroundColor,
    this.foregroundColor,
    this.bottom,
    this.centerTitle = true,
    this.toolbarHeight,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد الألوان الذكية بناءً على الثيم
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final effectiveBackgroundColor =
        backgroundColor ?? Theme.of(context).appBarTheme.backgroundColor;
    final effectiveForegroundColor = foregroundColor ??
        AppColors.getTextColorForBackground(
            effectiveBackgroundColor ?? AppColors.primary);

    return AppBar(
      title: Text(
        title,
        style: AppTypography(
          color: effectiveForegroundColor,
          fontSize: 20,
          fontWeight: FontWeight.w600,
          fontFamily: AppTypography.primaryFontFamily,
        ),
      ),
      centerTitle: centerTitle,
      elevation: elevation,
      backgroundColor: effectiveBackgroundColor,
      foregroundColor: effectiveForegroundColor,
      toolbarHeight: toolbarHeight,
      bottom: bottom,

      // تحديد الودجت في بداية الشريط
      leading: leading ??
          (showBackButton
              ? IconButton(
                  icon: const Icon(Icons.arrow_back_ios),
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                  color: effectiveForegroundColor,
                  tooltip: 'رجوع',
                )
              : (onMenuPressed != null
                  ? IconButton(
                      icon: const Icon(Icons.menu),
                      onPressed: onMenuPressed,
                      color: effectiveForegroundColor,
                      tooltip: 'القائمة الرئيسية',
                    )
                  : null)),

      actions: actions,
      iconTheme: IconThemeData(
        color: effectiveForegroundColor,
        size: AppDimensions.iconSizeMedium,
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
        (toolbarHeight ?? kToolbarHeight) +
            (bottom?.preferredSize.height ?? 0.0),
      );
}

// ========== الأزرار الموحدة ==========

/// أحجام الأزرار المتاحة
enum ButtonSize { small, medium, large }

/// زر موحد وذكي يدعم جميع الأنماط
class UnifiedButton extends StatelessWidget {
  /// نص الزر
  final String text;

  /// دالة عند الضغط
  final VoidCallback? onPressed;

  /// أيقونة الزر
  final IconData? icon;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون النص
  final Color? textColor;

  /// عرض الزر
  final double? width;

  /// ارتفاع الزر
  final double? height;

  /// هل الزر في حالة تحميل
  final bool isLoading;

  /// هل الزر محدد بإطار فقط
  final bool isOutlined;

  /// المسافة الداخلية
  final EdgeInsetsGeometry? padding;

  /// نصف قطر الحواف
  final BorderRadius? borderRadius;

  /// حجم الزر (صغير، متوسط، كبير)
  final ButtonSize size;

  const UnifiedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height,
    this.isLoading = false,
    this.isOutlined = false,
    this.padding,
    this.borderRadius,
    this.size = ButtonSize.medium,
  });

  /// مُنشئ مبسط لزر الحفظ
  const UnifiedButton.save({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.width,
  })  : icon = Icons.save,
        backgroundColor = AppColors.success,
        textColor = AppColors.onPrimary,
        height = null,
        isOutlined = false,
        padding = null,
        borderRadius = null,
        size = ButtonSize.medium;

  /// مُنشئ مبسط لزر الإلغاء
  const UnifiedButton.cancel({
    super.key,
    required this.text,
    required this.onPressed,
    this.width,
  })  : icon = Icons.cancel,
        backgroundColor = null,
        textColor = null,
        height = null,
        isLoading = false,
        isOutlined = true,
        padding = null,
        borderRadius = null,
        size = ButtonSize.medium;

  /// مُنشئ مبسط لزر الحذف
  const UnifiedButton.delete({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.width,
  })  : icon = Icons.delete,
        backgroundColor = AppColors.error,
        textColor = AppColors.onPrimary,
        height = null,
        isOutlined = false,
        padding = null,
        borderRadius = null,
        size = ButtonSize.medium;

  @override
  Widget build(BuildContext context) {
    // تحديد الخصائص بناءً على الحجم
    final buttonHeight = height ?? _getHeightForSize(size);
    final buttonPadding = padding ?? _getPaddingForSize(size);
    final fontSize = _getFontSizeForSize(size);
    final iconSize = _getIconSizeForSize(size);

    // تحديد الألوان الذكية
    final theme = Theme.of(context);
    final effectiveBackgroundColor = backgroundColor ?? theme.primaryColor;
    final effectiveTextColor = textColor ??
        (isOutlined
            ? effectiveBackgroundColor
            : AppColors.getTextColorForBackground(effectiveBackgroundColor));
    final effectiveBorderRadius =
        borderRadius ?? BorderRadius.circular(AppDimensions.defaultRadius);

    return SizedBox(
      width: width,
      height: buttonHeight,
      child: isOutlined
          ? OutlinedButton.icon(
              onPressed: isLoading ? null : onPressed,
              icon: _buildIcon(iconSize, effectiveTextColor),
              label: _buildLabel(fontSize, effectiveTextColor),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: effectiveBackgroundColor, width: 1.5),
                shape:
                    RoundedRectangleBorder(borderRadius: effectiveBorderRadius),
                padding: buttonPadding,
              ),
            )
          : ElevatedButton.icon(
              onPressed: isLoading ? null : onPressed,
              icon: _buildIcon(iconSize, effectiveTextColor),
              label: _buildLabel(fontSize, effectiveTextColor),
              style: ElevatedButton.styleFrom(
                backgroundColor: effectiveBackgroundColor,
                foregroundColor: effectiveTextColor,
                shape:
                    RoundedRectangleBorder(borderRadius: effectiveBorderRadius),
                padding: buttonPadding,
                elevation: isOutlined ? 0 : 2,
              ),
            ),
    );
  }

  /// بناء الأيقونة
  Widget _buildIcon(double iconSize, Color color) {
    if (isLoading) {
      return SizedBox(
        width: iconSize,
        height: iconSize,
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(color),
          strokeWidth: 2,
        ),
      );
    }

    if (icon != null) {
      return Icon(icon, size: iconSize, color: color);
    }

    return const SizedBox.shrink();
  }

  /// بناء النص
  Widget _buildLabel(double fontSize, Color color) {
    return Text(
      text,
      style: AppTypography(
        fontSize: fontSize,
        fontWeight: FontWeight.w600,
        color: color,
      ),
    );
  }

  /// الحصول على الارتفاع بناءً على الحجم
  double _getHeightForSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return AppDimensions.buttonHeightSmall;
      case ButtonSize.medium:
        return AppDimensions.buttonHeightMedium;
      case ButtonSize.large:
        return AppDimensions.buttonHeightLarge;
    }
  }

  /// الحصول على المسافة الداخلية بناءً على الحجم
  EdgeInsetsGeometry _getPaddingForSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return AppDimensions.buttonPaddingSmall;
      case ButtonSize.medium:
        return AppDimensions.buttonPaddingMedium;
      case ButtonSize.large:
        return AppDimensions.buttonPaddingLarge;
    }
  }

  /// الحصول على حجم الخط بناءً على الحجم
  double _getFontSizeForSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return AppDimensions.smallFontSize;
      case ButtonSize.medium:
        return AppDimensions.defaultFontSize;
      case ButtonSize.large:
        return AppDimensions.titleFontSize;
    }
  }

  /// الحصول على حجم الأيقونة بناءً على الحجم
  double _getIconSizeForSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return AppDimensions.smallIconSize;
      case ButtonSize.medium:
        return AppDimensions.defaultIconSize;
      case ButtonSize.large:
        return AppDimensions.mediumIconSize;
    }
  }
}

// ========== مؤشرات التحميل الموحدة ==========

/// مؤشر التحميل الموحد
/// يدعم عرض رسالة اختيارية مع المؤشر
class UnifiedLoadingIndicator extends StatelessWidget {
  /// حجم المؤشر
  final double size;

  /// لون المؤشر
  final Color? color;

  /// سمك الخط
  final double strokeWidth;

  /// رسالة التحميل الاختيارية
  final String? message;

  /// هل يتم عرض الرسالة أسفل المؤشر
  final bool showMessage;

  const UnifiedLoadingIndicator({
    super.key,
    this.size = 40.0,
    this.color,
    this.strokeWidth = 3.0,
    this.message,
    this.showMessage = true,
  });

  /// مُنشئ مبسط لمؤشر صغير
  const UnifiedLoadingIndicator.small({
    super.key,
    this.color,
    this.message,
  })  : size = 24.0,
        strokeWidth = 2.0,
        showMessage = false;

  /// مُنشئ مبسط لمؤشر كبير
  const UnifiedLoadingIndicator.large({
    super.key,
    this.color,
    this.message,
  })  : size = 60.0,
        strokeWidth = 4.0,
        showMessage = true;

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? Theme.of(context).primaryColor;

    if (!showMessage || message == null) {
      return SizedBox(
        width: size,
        height: size,
        child: CircularProgressIndicator(
          strokeWidth: strokeWidth,
          valueColor: AlwaysStoppedAnimation<Color>(effectiveColor),
        ),
      );
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: strokeWidth,
            valueColor: AlwaysStoppedAnimation<Color>(effectiveColor),
          ),
        ),
        const SizedBox(height: 16),
        Text(
          message!,
          textAlign: TextAlign.center,
          style: AppTypography(
            fontSize: 16,
            color: effectiveColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}

/// طبقة التحميل الموحدة
/// تعرض مؤشر التحميل فوق المحتوى
class UnifiedLoadingOverlay extends StatelessWidget {
  /// هل يتم عرض التحميل
  final bool isLoading;

  /// المحتوى الأساسي
  final Widget child;

  /// رسالة التحميل
  final String? message;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون المؤشر
  final Color? progressColor;

  /// شفافية الخلفية
  final double opacity;

  const UnifiedLoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.message,
    this.backgroundColor,
    this.progressColor,
    this.opacity = 0.5,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Positioned.fill(
            child: Container(
              color:
                  (backgroundColor ?? Colors.black).withValues(alpha: opacity),
              child: Center(
                child: UnifiedLoadingIndicator(
                  color: progressColor,
                  message: message,
                ),
              ),
            ),
          ),
      ],
    );
  }
}

// ========== حوارات التأكيد الموحدة ==========

/// حوار التأكيد الموحد
/// يدعم جميع أنواع الحوارات مع تصميم موحد
class UnifiedConfirmationDialog extends StatelessWidget {
  /// عنوان الحوار
  final String title;

  /// محتوى الحوار
  final String content;

  /// نص زر التأكيد
  final String confirmText;

  /// نص زر الإلغاء
  final String cancelText;

  /// دالة التأكيد
  final VoidCallback onConfirm;

  /// دالة الإلغاء
  final VoidCallback? onCancel;

  /// لون زر التأكيد
  final Color? confirmColor;

  /// أيقونة الحوار
  final IconData? icon;

  /// نوع الحوار (عادي، تحذير، خطر)
  final DialogType type;

  const UnifiedConfirmationDialog({
    super.key,
    required this.title,
    required this.content,
    this.confirmText = 'تأكيد',
    this.cancelText = 'إلغاء',
    required this.onConfirm,
    this.onCancel,
    this.confirmColor,
    this.icon,
    this.type = DialogType.normal,
  });

  /// مُنشئ مبسط لحوار الحذف
  const UnifiedConfirmationDialog.delete({
    super.key,
    required this.title,
    required this.content,
    required this.onConfirm,
    this.onCancel,
  })  : confirmText = 'حذف',
        cancelText = 'إلغاء',
        confirmColor = AppColors.error,
        icon = Icons.delete_outline,
        type = DialogType.danger;

  /// مُنشئ مبسط لحوار التحذير
  const UnifiedConfirmationDialog.warning({
    super.key,
    required this.title,
    required this.content,
    required this.onConfirm,
    this.onCancel,
  })  : confirmText = 'متابعة',
        cancelText = 'إلغاء',
        confirmColor = AppColors.warning,
        icon = Icons.warning_outlined,
        type = DialogType.warning;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveConfirmColor = confirmColor ?? _getColorForType(type);

    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
      ),
      title: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: effectiveConfirmColor,
              size: AppDimensions.mediumIconSize,
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Text(
              title,
              style: AppTypography(
                fontSize: AppDimensions.titleFontSize,
                fontWeight: FontWeight.bold,
                color: AppColors.getTextColorForBackground(
                    theme.dialogTheme.backgroundColor ??
                        theme.colorScheme.surface),
              ),
            ),
          ),
        ],
      ),
      content: Text(
        content,
        style: AppTypography(
          fontSize: AppDimensions.defaultFontSize,
          color: AppColors.getTextColorForBackground(
              theme.dialogTheme.backgroundColor ?? theme.colorScheme.surface),
        ),
      ),
      actions: [
        UnifiedButton.cancel(
          text: cancelText,
          onPressed: () {
            if (onCancel != null) {
              onCancel!();
            }
            Navigator.of(context).pop(false);
          },
        ),
        UnifiedButton(
          text: confirmText,
          backgroundColor: effectiveConfirmColor,
          onPressed: () {
            onConfirm();
            Navigator.of(context).pop(true);
          },
        ),
      ],
    );
  }

  /// الحصول على اللون بناءً على نوع الحوار
  Color _getColorForType(DialogType type) {
    switch (type) {
      case DialogType.normal:
        return AppColors.primary;
      case DialogType.warning:
        return AppColors.warning;
      case DialogType.danger:
        return AppColors.error;
    }
  }

  /// عرض حوار التأكيد
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String content,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    Color? confirmColor,
    IconData? icon,
    DialogType type = DialogType.normal,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => UnifiedConfirmationDialog(
        title: title,
        content: content,
        confirmText: confirmText,
        cancelText: cancelText,
        onConfirm: onConfirm ?? () {},
        onCancel: onCancel,
        confirmColor: confirmColor,
        icon: icon,
        type: type,
      ),
    );
  }

  /// عرض حوار تأكيد الحذف
  static Future<bool?> showDelete({
    required BuildContext context,
    required String itemName,
    String? content,
    VoidCallback? onConfirm,
  }) {
    return show(
      context: context,
      title: 'تأكيد الحذف',
      content: content ??
          'هل أنت متأكد من حذف $itemName؟\nلا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
      confirmColor: AppColors.error,
      icon: Icons.delete_outline,
      type: DialogType.danger,
      onConfirm: onConfirm,
    );
  }
}

/// أنواع الحوارات المتاحة
enum DialogType { normal, warning, danger }

// ========== حقول النصوص الموحدة ==========

/// حقل النص الموحد
/// يدعم جميع أنواع حقول النصوص مع تصميم موحد
class UnifiedTextField extends StatelessWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String? label;

  /// نص التلميح
  final String? hint;

  /// نص الخطأ
  final String? errorText;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// هل النص مخفي (لكلمات المرور)
  final bool obscureText;

  /// هل الحقل للقراءة فقط
  final bool readOnly;

  /// هل الحقل مفعل
  final bool enabled;

  /// نوع لوحة المفاتيح
  final TextInputType keyboardType;

  /// إجراء لوحة المفاتيح
  final TextInputAction textInputAction;

  /// عدد الأسطر الأقصى
  final int? maxLines;

  /// عدد الأسطر الأدنى
  final int? minLines;

  /// الحد الأقصى لعدد الأحرف
  final int? maxLength;

  /// دالة التحقق من صحة المدخلات
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<String>? onChanged;

  /// دالة تنفذ عند الضغط على الحقل
  final VoidCallback? onTap;

  /// أيقونة في بداية الحقل
  final IconData? prefixIcon;

  /// ودجت في بداية الحقل
  final Widget? prefixWidget;

  /// أيقونة في نهاية الحقل
  final IconData? suffixIcon;

  /// ودجت في نهاية الحقل
  final Widget? suffixWidget;

  /// دالة تنفذ عند الضغط على أيقونة النهاية
  final VoidCallback? onSuffixIconPressed;

  /// منسقات الإدخال
  final List<TextInputFormatter>? inputFormatters;

  /// المسافة الداخلية للمحتوى
  final EdgeInsetsGeometry? contentPadding;

  /// نقطة التركيز
  final FocusNode? focusNode;

  /// هل يتم التركيز تلقائياً
  final bool autofocus;

  /// محاذاة النص
  final TextAlign textAlign;

  const UnifiedTextField({
    super.key,
    this.controller,
    this.label,
    this.hint,
    this.errorText,
    this.isRequired = false,
    this.obscureText = false,
    this.readOnly = false,
    this.enabled = true,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.validator,
    this.onChanged,
    this.onTap,
    this.prefixIcon,
    this.prefixWidget,
    this.suffixIcon,
    this.suffixWidget,
    this.onSuffixIconPressed,
    this.inputFormatters,
    this.contentPadding,
    this.focusNode,
    this.autofocus = false,
    this.textAlign = TextAlign.start,
  });

  /// مُنشئ مبسط لحقل البحث
  const UnifiedTextField.search({
    super.key,
    required this.controller,
    this.hint = 'بحث...',
    this.onChanged,
    VoidCallback? onClear,
  })  : label = null,
        errorText = null,
        isRequired = false,
        obscureText = false,
        readOnly = false,
        enabled = true,
        keyboardType = TextInputType.text,
        textInputAction = TextInputAction.search,
        maxLines = 1,
        minLines = null,
        maxLength = null,
        validator = null,
        onTap = null,
        prefixIcon = Icons.search,
        prefixWidget = null,
        suffixIcon = Icons.clear,
        suffixWidget = null,
        onSuffixIconPressed = onClear,
        inputFormatters = null,
        contentPadding = null,
        focusNode = null,
        autofocus = false,
        textAlign = TextAlign.start;

  /// مُنشئ مبسط لحقل كلمة المرور
  const UnifiedTextField.password({
    super.key,
    required this.controller,
    this.label = 'كلمة المرور',
    this.hint,
    this.isRequired = true,
    this.validator,
    this.onChanged,
  })  : errorText = null,
        obscureText = true,
        readOnly = false,
        enabled = true,
        keyboardType = TextInputType.visiblePassword,
        textInputAction = TextInputAction.done,
        maxLines = 1,
        minLines = null,
        maxLength = null,
        onTap = null,
        prefixIcon = Icons.lock_outline,
        prefixWidget = null,
        suffixIcon = Icons.visibility,
        suffixWidget = null,
        onSuffixIconPressed = null,
        inputFormatters = null,
        contentPadding = null,
        focusNode = null,
        autofocus = false,
        textAlign = TextAlign.start;

  /// مُنشئ مبسط لحقل الأرقام
  const UnifiedTextField.number({
    super.key,
    required this.controller,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.validator,
    this.onChanged,
  })  : errorText = null,
        obscureText = false,
        readOnly = false,
        enabled = true,
        keyboardType = TextInputType.number,
        textInputAction = TextInputAction.next,
        maxLines = 1,
        minLines = null,
        maxLength = null,
        onTap = null,
        prefixIcon = Icons.numbers,
        prefixWidget = null,
        suffixIcon = null,
        suffixWidget = null,
        onSuffixIconPressed = null,
        inputFormatters = null,
        contentPadding = null,
        focusNode = null,
        autofocus = false,
        textAlign = TextAlign.start;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // تسمية الحقل
        if (label != null && label!.isNotEmpty) ...[
          Text(
            isRequired ? '$label *' : label!,
            style: AppTypography(
              fontSize: AppDimensions.smallFontSize,
              fontWeight: FontWeight.w600,
              color: AppColors.getAdaptiveTextColor(isDark),
            ),
          ),
          const SizedBox(height: 8),
        ],

        // حقل النص
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            labelText: label == null ? null : (isRequired ? '$label *' : label),
            hintText: hint,
            errorText: errorText,
            prefixIcon: prefixIcon != null ? Icon(prefixIcon) : prefixWidget,
            suffixIcon: _buildSuffixIcon(),
            contentPadding: contentPadding ??
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              borderSide: BorderSide(
                color: AppColors.getAdaptiveBorderColor(isDark),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              borderSide: const BorderSide(
                color: AppColors.error,
                width: 1,
              ),
            ),
            filled: true,
            fillColor: isDark
                ? AppColors.darkSurfaceVariant
                : AppColors.lightSurfaceVariant,
          ),
          obscureText: obscureText,
          readOnly: readOnly,
          enabled: enabled,
          keyboardType: keyboardType,
          textInputAction: textInputAction,
          maxLines: maxLines,
          minLines: minLines,
          maxLength: maxLength,
          validator: validator ?? (isRequired ? _defaultValidator : null),
          onChanged: onChanged,
          onTap: onTap,
          inputFormatters: inputFormatters,
          focusNode: focusNode,
          autofocus: autofocus,
          textAlign: textAlign,
          style: AppTypography(
            fontSize: AppDimensions.defaultFontSize,
            color: AppColors.getAdaptiveTextColor(isDark),
          ),
        ),
      ],
    );
  }

  /// بناء أيقونة النهاية
  Widget? _buildSuffixIcon() {
    if (suffixWidget != null) return suffixWidget;

    if (suffixIcon != null) {
      return IconButton(
        icon: Icon(suffixIcon),
        onPressed: onSuffixIconPressed,
      );
    }

    return null;
  }

  /// التحقق الافتراضي للحقول المطلوبة
  String? _defaultValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'هذا الحقل مطلوب';
    }
    return null;
  }
}

// ========== حالات الواجهة الموحدة ==========

/// ودجت الحالة الفارغة الموحد
/// يعرض رسالة ورمز عندما لا توجد بيانات
class UnifiedEmptyState extends StatelessWidget {
  /// الرسالة المعروضة
  final String message;

  /// الأيقونة المعروضة
  final IconData icon;

  /// نص الزر الاختياري
  final String? buttonText;

  /// دالة الزر الاختياري
  final VoidCallback? onButtonPressed;

  /// دالة التحديث (للسحب للتحديث)
  final VoidCallback? onRefresh;

  /// لون الأيقونة والنص
  final Color? color;

  const UnifiedEmptyState({
    super.key,
    required this.message,
    this.icon = Icons.inbox_outlined,
    this.buttonText,
    this.onButtonPressed,
    this.onRefresh,
    this.color,
  });

  /// مُنشئ مبسط لحالة عدم وجود بيانات
  const UnifiedEmptyState.noData({
    super.key,
    this.message = 'لا توجد بيانات',
    this.buttonText,
    this.onButtonPressed,
    this.onRefresh,
  })  : icon = Icons.inbox_outlined,
        color = null;

  /// مُنشئ مبسط لحالة عدم وجود نتائج بحث
  const UnifiedEmptyState.noSearchResults({
    super.key,
    this.message = 'لا توجد نتائج للبحث',
    this.onRefresh,
  })  : icon = Icons.search_off,
        buttonText = null,
        onButtonPressed = null,
        color = null;

  /// مُنشئ مبسط لحالة الخطأ
  const UnifiedEmptyState.error({
    super.key,
    this.message = 'حدث خطأ أثناء تحميل البيانات',
    this.buttonText = 'إعادة المحاولة',
    this.onButtonPressed,
    this.onRefresh,
  })  : icon = Icons.error_outline,
        color = AppColors.error;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final effectiveColor =
        color ?? AppColors.getAdaptiveSecondaryTextColor(isDark);

    Widget content = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // الأيقونة
        Icon(
          icon,
          size: 80,
          color: effectiveColor.withValues(alpha: 0.6),
        ),

        const SizedBox(height: 24),

        // الرسالة
        Text(
          message,
          textAlign: TextAlign.center,
          style: AppTypography(
            fontSize: AppDimensions.titleFontSize,
            fontWeight: FontWeight.w500,
            color: effectiveColor,
          ),
        ),

        const SizedBox(height: 32),

        // الزر الاختياري
        if (buttonText != null && onButtonPressed != null)
          UnifiedButton(
            text: buttonText!,
            onPressed: onButtonPressed,
            backgroundColor: Theme.of(context).primaryColor,
            icon: Icons.refresh,
          ),
      ],
    );

    // إضافة السحب للتحديث إذا كان متاحاً
    if (onRefresh != null) {
      content = RefreshIndicator(
        onRefresh: () async {
          onRefresh!();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.6,
            child: Center(child: content),
          ),
        ),
      );
    }

    return Center(child: content);
  }
}

/// ودجت حالة الخطأ الموحد
/// يعرض رسالة خطأ مع إمكانية إعادة المحاولة
class UnifiedErrorState extends StatelessWidget {
  /// رسالة الخطأ
  final String message;

  /// تفاصيل الخطأ الاختيارية
  final String? details;

  /// دالة إعادة المحاولة
  final VoidCallback? onRetry;

  /// نص زر إعادة المحاولة
  final String retryText;

  const UnifiedErrorState({
    super.key,
    required this.message,
    this.details,
    this.onRetry,
    this.retryText = 'إعادة المحاولة',
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة الخطأ
            const Icon(
              Icons.error_outline,
              size: 80,
              color: AppColors.error,
            ),

            const SizedBox(height: 24),

            // رسالة الخطأ
            Text(
              message,
              textAlign: TextAlign.center,
              style: const AppTypography(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.error,
              ),
            ),

            // تفاصيل الخطأ
            if (details != null) ...[
              const SizedBox(height: 12),
              Text(
                details!,
                textAlign: TextAlign.center,
                style: AppTypography(
                  fontSize: 14,
                  color: AppColors.getAdaptiveSecondaryTextColor(
                      Theme.of(context).brightness == Brightness.dark),
                ),
              ),
            ],

            const SizedBox(height: 32),

            // زر إعادة المحاولة
            if (onRetry != null)
              UnifiedButton(
                text: retryText,
                onPressed: onRetry,
                backgroundColor: AppColors.error,
                icon: Icons.refresh,
              ),
          ],
        ),
      ),
    );
  }
}
