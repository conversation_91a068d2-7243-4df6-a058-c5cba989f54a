import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../theme/index.dart';
//import '../utils/index.dart';

/// ملف الودجات المشتركة الموحد
/// يحتوي على جميع الودجات الأساسية المستخدمة عبر التطبيق
/// تم توحيدها لتجنب التكرار وضمان التناسق

// ========== شريط التطبيق الموحد ==========

/// شريط التطبيق الموحد والذكي
/// يدعم جميع الخصائص المطلوبة مع التكيف مع الثيم
class UnifiedAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// عنوان الشريط
  final String title;

  /// الإجراءات في الشريط
  final List<Widget>? actions;

  /// هل يتم عرض زر الرجوع
  final bool showBackButton;

  /// دالة عند الضغط على زر القائمة
  final VoidCallback? onMenuPressed;

  /// دالة عند الضغط على زر الرجوع
  final VoidCallback? onBackPressed;

  /// ودجت مخصص في بداية الشريط
  final Widget? leading;

  /// ارتفاع الظل
  final double elevation;

  /// لون الخلفية المخصص
  final Color? backgroundColor;

  /// لون النص المخصص
  final Color? foregroundColor;

  /// ودجت في أسفل الشريط
  final PreferredSizeWidget? bottom;

  /// هل يتم توسيط العنوان
  final bool centerTitle;

  /// ارتفاع الشريط المخصص
  final double? toolbarHeight;

  const UnifiedAppBar({
    super.key,
    required this.title,
    this.actions,
    this.showBackButton = true,
    this.onMenuPressed,
    this.onBackPressed,
    this.leading,
    this.elevation = 0,
    this.backgroundColor,
    this.foregroundColor,
    this.bottom,
    this.centerTitle = true,
    this.toolbarHeight,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد الألوان الذكية بناءً على الثيم
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final effectiveBackgroundColor =
        backgroundColor ?? Theme.of(context).appBarTheme.backgroundColor;
    final effectiveForegroundColor = foregroundColor ??
        AppColors.getTextColorForBackground(
            effectiveBackgroundColor ?? AppColors.primary);

    return AppBar(
      title: Text(
        title,
        style: AppTypography(
          color: effectiveForegroundColor,
          fontSize: AppDimensions.titleFontSize,
          fontWeight: FontWeight.w600,
          fontFamily: AppTypography.primaryFontFamily,
        ),
      ),
      centerTitle: centerTitle,
      elevation: elevation,
      backgroundColor: effectiveBackgroundColor,
      foregroundColor: effectiveForegroundColor,
      toolbarHeight: toolbarHeight,
      bottom: bottom,

      // تحديد الودجت في بداية الشريط
      leading: leading ??
          (showBackButton
              ? IconButton(
                  icon: const Icon(Icons.arrow_back_ios),
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                  color: effectiveForegroundColor,
                  tooltip: 'رجوع',
                )
              : (onMenuPressed != null
                  ? IconButton(
                      icon: const Icon(Icons.menu),
                      onPressed: onMenuPressed,
                      color: effectiveForegroundColor,
                      tooltip: 'القائمة الرئيسية',
                    )
                  : null)),

      actions: actions,
      iconTheme: IconThemeData(
        color: effectiveForegroundColor,
        size: AppDimensions.iconSizeMedium,
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
        (toolbarHeight ?? kToolbarHeight) +
            (bottom?.preferredSize.height ?? 0.0),
      );
}

// ========== الأزرار الموحدة ==========

/// أحجام الأزرار المتاحة
enum ButtonSize { small, medium, large }

/// زر موحد وذكي يدعم جميع الأنماط
class UnifiedButton extends StatelessWidget {
  /// نص الزر
  final String text;

  /// دالة عند الضغط
  final VoidCallback? onPressed;

  /// أيقونة الزر
  final IconData? icon;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون النص
  final Color? textColor;

  /// عرض الزر
  final double? width;

  /// ارتفاع الزر
  final double? height;

  /// هل الزر في حالة تحميل
  final bool isLoading;

  /// هل الزر محدد بإطار فقط
  final bool isOutlined;

  /// المسافة الداخلية
  final EdgeInsetsGeometry? padding;

  /// نصف قطر الحواف
  final BorderRadius? borderRadius;

  /// حجم الزر (صغير، متوسط، كبير)
  final ButtonSize size;

  const UnifiedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height,
    this.isLoading = false,
    this.isOutlined = false,
    this.padding,
    this.borderRadius,
    this.size = ButtonSize.medium,
  });

  /// مُنشئ مبسط لزر الحفظ
  const UnifiedButton.save({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.width,
  })  : icon = Icons.save,
        backgroundColor = AppColors.success,
        textColor = AppColors.onPrimary,
        height = null,
        isOutlined = false,
        padding = null,
        borderRadius = null,
        size = ButtonSize.medium;

  /// مُنشئ مبسط لزر الإلغاء
  const UnifiedButton.cancel({
    super.key,
    required this.text,
    required this.onPressed,
    this.width,
  })  : icon = Icons.cancel,
        backgroundColor = null,
        textColor = null,
        height = null,
        isLoading = false,
        isOutlined = true,
        padding = null,
        borderRadius = null,
        size = ButtonSize.medium;

  /// مُنشئ مبسط لزر الحذف
  const UnifiedButton.delete({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.width,
  })  : icon = Icons.delete,
        backgroundColor = AppColors.error,
        textColor = AppColors.onPrimary,
        height = null,
        isOutlined = false,
        padding = null,
        borderRadius = null,
        size = ButtonSize.medium;

  @override
  Widget build(BuildContext context) {
    // تحديد الخصائص بناءً على الحجم
    final buttonHeight = height ?? _getHeightForSize(size);
    final buttonPadding = padding ?? _getPaddingForSize(size);
    final fontSize = _getFontSizeForSize(size);
    final iconSize = _getIconSizeForSize(size);

    // تحديد الألوان الذكية
    final theme = Theme.of(context);
    final effectiveBackgroundColor = backgroundColor ?? theme.primaryColor;
    final effectiveTextColor = textColor ??
        (isOutlined
            ? effectiveBackgroundColor
            : AppColors.getTextColorForBackground(effectiveBackgroundColor));
    final effectiveBorderRadius =
        borderRadius ?? BorderRadius.circular(AppDimensions.defaultRadius);

    return SizedBox(
      width: width,
      height: buttonHeight,
      child: isOutlined
          ? OutlinedButton.icon(
              onPressed: isLoading ? null : onPressed,
              icon: _buildIcon(iconSize, effectiveTextColor),
              label: _buildLabel(fontSize, effectiveTextColor),
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                    color: effectiveBackgroundColor,
                    width: AppDimensions.spacing2),
                shape:
                    RoundedRectangleBorder(borderRadius: effectiveBorderRadius),
                padding: buttonPadding,
              ),
            )
          : ElevatedButton.icon(
              onPressed: isLoading ? null : onPressed,
              icon: _buildIcon(iconSize, effectiveTextColor),
              label: _buildLabel(fontSize, effectiveTextColor),
              style: ElevatedButton.styleFrom(
                backgroundColor: effectiveBackgroundColor,
                foregroundColor: effectiveTextColor,
                shape:
                    RoundedRectangleBorder(borderRadius: effectiveBorderRadius),
                padding: buttonPadding,
                elevation: isOutlined
                    ? AppDimensions.elevationNone
                    : AppDimensions.elevationLow,
              ),
            ),
    );
  }

  /// بناء الأيقونة
  Widget _buildIcon(double iconSize, Color color) {
    if (isLoading) {
      return SizedBox(
        width: iconSize,
        height: iconSize,
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(color),
          strokeWidth: AppDimensions.spacing2,
        ),
      );
    }

    if (icon != null) {
      return Icon(icon, size: iconSize, color: color);
    }

    return const SizedBox.shrink();
  }

  /// بناء النص
  Widget _buildLabel(double fontSize, Color color) {
    return Text(
      text,
      style: AppTypography(
        fontSize: fontSize,
        fontWeight: FontWeight.w600,
        color: color,
      ),
    );
  }

  /// الحصول على الارتفاع بناءً على الحجم
  double _getHeightForSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return AppDimensions.buttonHeightSmall;
      case ButtonSize.medium:
        return AppDimensions.buttonHeightMedium;
      case ButtonSize.large:
        return AppDimensions.buttonHeightLarge;
    }
  }

  /// الحصول على المسافة الداخلية بناءً على الحجم
  EdgeInsetsGeometry _getPaddingForSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return AppDimensions.buttonPaddingSmall;
      case ButtonSize.medium:
        return AppDimensions.buttonPaddingMedium;
      case ButtonSize.large:
        return AppDimensions.buttonPaddingLarge;
    }
  }

  /// الحصول على حجم الخط بناءً على الحجم
  double _getFontSizeForSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return AppDimensions.smallFontSize;
      case ButtonSize.medium:
        return AppDimensions.defaultFontSize;
      case ButtonSize.large:
        return AppDimensions.titleFontSize;
    }
  }

  /// الحصول على حجم الأيقونة بناءً على الحجم
  double _getIconSizeForSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return AppDimensions.smallIconSize;
      case ButtonSize.medium:
        return AppDimensions.defaultIconSize;
      case ButtonSize.large:
        return AppDimensions.mediumIconSize;
    }
  }
}

// ========== مؤشرات التحميل الموحدة ==========

/// مؤشر التحميل الموحد
/// يدعم عرض رسالة اختيارية مع المؤشر
class UnifiedLoadingIndicator extends StatelessWidget {
  /// حجم المؤشر
  final double size;

  /// لون المؤشر
  final Color? color;

  /// سمك الخط
  final double strokeWidth;

  /// رسالة التحميل الاختيارية
  final String? message;

  /// هل يتم عرض الرسالة أسفل المؤشر
  final bool showMessage;

  const UnifiedLoadingIndicator({
    super.key,
    this.size = AppDimensions.iconSizeLarge,
    this.color,
    this.strokeWidth = AppDimensions.spacing4,
    this.message,
    this.showMessage = true,
  });

  /// مُنشئ مبسط لمؤشر صغير
  const UnifiedLoadingIndicator.small({
    super.key,
    this.color,
    this.message,
  })  : size = AppDimensions.iconSizeMedium,
        strokeWidth = AppDimensions.spacing2,
        showMessage = false;

  /// مُنشئ مبسط لمؤشر كبير
  const UnifiedLoadingIndicator.large({
    super.key,
    this.color,
    this.message,
  })  : size = AppDimensions.iconSizeXLarge,
        strokeWidth = AppDimensions.spacing4,
        showMessage = true;

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? Theme.of(context).primaryColor;

    if (!showMessage || message == null) {
      return SizedBox(
        width: size,
        height: size,
        child: CircularProgressIndicator(
          strokeWidth: strokeWidth,
          valueColor: AlwaysStoppedAnimation<Color>(effectiveColor),
        ),
      );
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: strokeWidth,
            valueColor: AlwaysStoppedAnimation<Color>(effectiveColor),
          ),
        ),
        const SizedBox(height: AppDimensions.spacing16),
        Text(
          message!,
          textAlign: TextAlign.center,
          style: AppTypography(
            fontSize: AppDimensions.mediumFontSize,
            color: effectiveColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}

/// طبقة التحميل الموحدة
/// تعرض مؤشر التحميل فوق المحتوى
class UnifiedLoadingOverlay extends StatelessWidget {
  /// هل يتم عرض التحميل
  final bool isLoading;

  /// المحتوى الأساسي
  final Widget child;

  /// رسالة التحميل
  final String? message;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون المؤشر
  final Color? progressColor;

  /// شفافية الخلفية
  final double opacity;

  const UnifiedLoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.message,
    this.backgroundColor,
    this.progressColor,
    this.opacity = 0.5, // يمكن الاحتفاظ بهذا لأنه نسبة شفافية
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Positioned.fill(
            child: Container(
              color: (backgroundColor ?? AppColors.secondary)
                  .withValues(alpha: opacity),
              child: Center(
                child: UnifiedLoadingIndicator(
                  color: progressColor,
                  message: message,
                ),
              ),
            ),
          ),
      ],
    );
  }
}

// ========== حوارات التأكيد الموحدة ==========

/// حوار التأكيد الموحد
/// يدعم جميع أنواع الحوارات مع تصميم موحد
class UnifiedConfirmationDialog extends StatelessWidget {
  /// عنوان الحوار
  final String title;

  /// محتوى الحوار
  final String content;

  /// نص زر التأكيد
  final String confirmText;

  /// نص زر الإلغاء
  final String cancelText;

  /// دالة التأكيد
  final VoidCallback onConfirm;

  /// دالة الإلغاء
  final VoidCallback? onCancel;

  /// لون زر التأكيد
  final Color? confirmColor;

  /// أيقونة الحوار
  final IconData? icon;

  /// نوع الحوار (عادي، تحذير، خطر)
  final DialogType type;

  const UnifiedConfirmationDialog({
    super.key,
    required this.title,
    required this.content,
    this.confirmText = 'تأكيد',
    this.cancelText = 'إلغاء',
    required this.onConfirm,
    this.onCancel,
    this.confirmColor,
    this.icon,
    this.type = DialogType.normal,
  });

  /// مُنشئ مبسط لحوار الحذف
  const UnifiedConfirmationDialog.delete({
    super.key,
    required this.title,
    required this.content,
    required this.onConfirm,
    this.onCancel,
  })  : confirmText = 'حذف',
        cancelText = 'إلغاء',
        confirmColor = AppColors.error,
        icon = Icons.delete_outline,
        type = DialogType.danger;

  /// مُنشئ مبسط لحوار التحذير
  const UnifiedConfirmationDialog.warning({
    super.key,
    required this.title,
    required this.content,
    required this.onConfirm,
    this.onCancel,
  })  : confirmText = 'متابعة',
        cancelText = 'إلغاء',
        confirmColor = AppColors.warning,
        icon = Icons.warning_outlined,
        type = DialogType.warning;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveConfirmColor = confirmColor ?? _getColorForType(type);

    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
      ),
      title: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: effectiveConfirmColor,
              size: AppDimensions.mediumIconSize,
            ),
            const SizedBox(width: AppDimensions.spacing12),
          ],
          Expanded(
            child: Text(
              title,
              style: AppTypography(
                fontSize: AppDimensions.titleFontSize,
                fontWeight: FontWeight.bold,
                color: AppColors.getTextColorForBackground(
                    theme.dialogTheme.backgroundColor ??
                        theme.colorScheme.surface),
              ),
            ),
          ),
        ],
      ),
      content: Text(
        content,
        style: AppTypography(
          fontSize: AppDimensions.defaultFontSize,
          color: AppColors.getTextColorForBackground(
              theme.dialogTheme.backgroundColor ?? theme.colorScheme.surface),
        ),
      ),
      actions: [
        UnifiedButton.cancel(
          text: cancelText,
          onPressed: () {
            if (onCancel != null) {
              onCancel!();
            }
            Navigator.of(context).pop(false);
          },
        ),
        UnifiedButton(
          text: confirmText,
          backgroundColor: effectiveConfirmColor,
          onPressed: () {
            onConfirm();
            Navigator.of(context).pop(true);
          },
        ),
      ],
    );
  }

  /// الحصول على اللون بناءً على نوع الحوار
  Color _getColorForType(DialogType type) {
    switch (type) {
      case DialogType.normal:
        return AppColors.primary;
      case DialogType.warning:
        return AppColors.warning;
      case DialogType.danger:
        return AppColors.error;
    }
  }

  /// عرض حوار التأكيد
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String content,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    Color? confirmColor,
    IconData? icon,
    DialogType type = DialogType.normal,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => UnifiedConfirmationDialog(
        title: title,
        content: content,
        confirmText: confirmText,
        cancelText: cancelText,
        onConfirm: onConfirm ?? () {},
        onCancel: onCancel,
        confirmColor: confirmColor,
        icon: icon,
        type: type,
      ),
    );
  }

  /// عرض حوار تأكيد الحذف
  static Future<bool?> showDelete({
    required BuildContext context,
    required String itemName,
    String? content,
    VoidCallback? onConfirm,
  }) {
    return show(
      context: context,
      title: 'تأكيد الحذف',
      content: content ??
          'هل أنت متأكد من حذف $itemName؟\nلا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
      confirmColor: AppColors.error,
      icon: Icons.delete_outline,
      type: DialogType.danger,
      onConfirm: onConfirm,
    );
  }
}

/// أنواع الحوارات المتاحة
enum DialogType { normal, warning, danger }

// ========== حقول النصوص المتخصصة ==========

/// حقل رقم الهاتف الموحد مع التنسيق والتحقق التلقائي
class UnifiedPhoneField extends StatefulWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String? label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// دالة التحقق المخصصة
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<String>? onChanged;

  /// رمز البلد الافتراضي
  final String defaultCountryCode;

  /// هل يتم عرض رمز البلد
  final bool showCountryCode;

  /// هل يتم التنسيق التلقائي
  final bool autoFormat;

  const UnifiedPhoneField({
    super.key,
    this.controller,
    this.label = 'رقم الهاتف',
    this.hint = 'أدخل رقم الهاتف',
    this.isRequired = false,
    this.validator,
    this.onChanged,
    this.defaultCountryCode = '+966',
    this.showCountryCode = true,
    this.autoFormat = true,
  });

  @override
  State<UnifiedPhoneField> createState() => _UnifiedPhoneFieldState();
}

class _UnifiedPhoneFieldState extends State<UnifiedPhoneField> {
  late TextEditingController _controller;
  String _countryCode = '+966';

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _countryCode = widget.defaultCountryCode;
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedTextField(
      controller: _controller,
      label: widget.label,
      hint: widget.hint,
      isRequired: widget.isRequired,
      keyboardType: TextInputType.phone,
      prefixIcon: Icons.phone,
      prefixWidget: widget.showCountryCode ? _buildCountryCodePrefix() : null,
      validator: widget.validator ?? _defaultPhoneValidator,
      onChanged: (value) {
        if (widget.autoFormat) {
          _formatPhoneNumber(value);
        }
        widget.onChanged?.call(value);
      },
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9\s\-\(\)]')),
      ],
    );
  }

  /// بناء بادئة رمز البلد
  Widget _buildCountryCodePrefix() {
    return Container(
      padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.spacing12,
          vertical: AppDimensions.spacing8),
      child: Text(
        _countryCode,
        style: AppTypography(
          fontSize: AppDimensions.mediumFontSize,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// تنسيق رقم الهاتف تلقائياً
  void _formatPhoneNumber(String value) {
    // إزالة جميع الرموز والمسافات
    String cleanNumber = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // تطبيق التنسيق حسب رمز البلد
    if (_countryCode == '+966') {
      // تنسيق الأرقام السعودية
      if (cleanNumber.length >= 9) {
        String formatted =
            '${cleanNumber.substring(0, 3)} ${cleanNumber.substring(3, 6)} ${cleanNumber.substring(6)}';
        if (_controller.text != formatted) {
          _controller.value = TextEditingValue(
            text: formatted,
            selection: TextSelection.collapsed(offset: formatted.length),
          );
        }
      }
    }
  }

  /// التحقق الافتراضي من رقم الهاتف
  String? _defaultPhoneValidator(String? value) {
    if (widget.isRequired && (value == null || value.trim().isEmpty)) {
      return 'رقم الهاتف مطلوب';
    }

    if (value != null && value.isNotEmpty) {
      // إزالة المسافات والرموز
      final cleanPhone = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');

      // التحقق من الأرقام السعودية
      if (_countryCode == '+966') {
        final saudiPhoneRegExp = RegExp(r'^(05)[0-9]{8}$');
        if (!saudiPhoneRegExp.hasMatch(cleanPhone)) {
          return 'رقم الهاتف السعودي غير صحيح';
        }
      } else {
        // التحقق من الأرقام الدولية العامة
        final internationalPhoneRegExp = RegExp(r'^[0-9]{8,15}$');
        if (!internationalPhoneRegExp.hasMatch(cleanPhone)) {
          return 'رقم الهاتف غير صحيح';
        }
      }
    }

    return null;
  }
}

/// حقل البريد الإلكتروني الموحد مع التحقق المتقدم
class UnifiedEmailField extends StatelessWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String? label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// دالة التحقق المخصصة
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<String>? onChanged;

  const UnifiedEmailField({
    super.key,
    this.controller,
    this.label = 'البريد الإلكتروني',
    this.hint = '<EMAIL>',
    this.isRequired = false,
    this.validator,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return UnifiedTextField(
      controller: controller,
      label: label,
      hint: hint,
      isRequired: isRequired,
      keyboardType: TextInputType.emailAddress,
      prefixIcon: Icons.email_outlined,
      validator: validator ?? _defaultEmailValidator,
      onChanged: onChanged,
      textInputAction: TextInputAction.next,
    );
  }

  /// التحقق الافتراضي من البريد الإلكتروني
  String? _defaultEmailValidator(String? value) {
    if (isRequired && (value == null || value.trim().isEmpty)) {
      return 'البريد الإلكتروني مطلوب';
    }

    if (value != null && value.isNotEmpty) {
      final emailRegExp =
          RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
      if (!emailRegExp.hasMatch(value.trim())) {
        return 'البريد الإلكتروني غير صحيح';
      }
    }

    return null;
  }
}

/// حقل المبلغ المالي الموحد مع التنسيق والعملة
class UnifiedCurrencyField extends StatefulWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String? label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// دالة التحقق المخصصة
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<double?>? onChanged;

  /// رمز العملة
  final String currencySymbol;

  /// عدد الخانات العشرية
  final int decimalPlaces;

  /// الحد الأدنى للقيمة
  final double? minValue;

  /// الحد الأقصى للقيمة
  final double? maxValue;

  /// هل يتم عرض فاصل الآلاف
  final bool showThousandsSeparator;

  const UnifiedCurrencyField({
    super.key,
    this.controller,
    this.label = 'المبلغ',
    this.hint = '0.00',
    this.isRequired = false,
    this.validator,
    this.onChanged,
    this.currencySymbol = 'ر.ي',
    this.decimalPlaces = 2,
    this.minValue,
    this.maxValue,
    this.showThousandsSeparator = true,
  });

  @override
  State<UnifiedCurrencyField> createState() => _UnifiedCurrencyFieldState();
}

class _UnifiedCurrencyFieldState extends State<UnifiedCurrencyField> {
  late TextEditingController _controller;
  final NumberFormat _formatter = NumberFormat('#,##0.00', 'ar');

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedTextField(
      controller: _controller,
      label: widget.label,
      hint: widget.hint,
      isRequired: widget.isRequired,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      prefixIcon: Icons.monetization_on,
      suffixWidget: _buildCurrencySymbol(),
      validator: widget.validator ?? _defaultCurrencyValidator,
      onChanged: (value) {
        _formatCurrency(value);
        final numericValue = _parseNumericValue(value);
        widget.onChanged?.call(numericValue);
      },
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
      ],
    );
  }

  /// بناء رمز العملة
  Widget _buildCurrencySymbol() {
    return Container(
      padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.spacing12,
          vertical: AppDimensions.spacing8),
      child: Text(
        widget.currencySymbol,
        style: AppTypography(
          fontSize: AppDimensions.mediumFontSize,
          fontWeight: AppTypography.weightSemiBold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  /// تنسيق العملة
  void _formatCurrency(String value) {
    if (value.isEmpty) return;

    final numericValue = _parseNumericValue(value);
    if (numericValue != null && widget.showThousandsSeparator) {
      final formatted = _formatter.format(numericValue);
      if (_controller.text != formatted) {
        _controller.value = TextEditingValue(
          text: formatted,
          selection: TextSelection.collapsed(offset: formatted.length),
        );
      }
    }
  }

  /// تحويل النص إلى قيمة رقمية
  double? _parseNumericValue(String value) {
    if (value.isEmpty) return null;

    // إزالة فواصل الآلاف والرموز
    final cleanValue =
        value.replaceAll(',', '').replaceAll(widget.currencySymbol, '').trim();
    return double.tryParse(cleanValue);
  }

  /// التحقق الافتراضي من المبلغ
  String? _defaultCurrencyValidator(String? value) {
    if (widget.isRequired && (value == null || value.trim().isEmpty)) {
      return 'المبلغ مطلوب';
    }

    if (value != null && value.isNotEmpty) {
      final numericValue = _parseNumericValue(value);

      if (numericValue == null) {
        return 'المبلغ غير صحيح';
      }

      if (widget.minValue != null && numericValue < widget.minValue!) {
        return 'المبلغ يجب أن يكون أكبر من ${widget.minValue}';
      }

      if (widget.maxValue != null && numericValue > widget.maxValue!) {
        return 'المبلغ يجب أن يكون أقل من ${widget.maxValue}';
      }
    }

    return null;
  }
}

/// حقل النسبة المئوية الموحد
class UnifiedPercentageField extends StatelessWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String? label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// دالة التحقق المخصصة
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<double?>? onChanged;

  /// الحد الأدنى للنسبة (افتراضي 0)
  final double minValue;

  /// الحد الأقصى للنسبة (افتراضي 100)
  final double maxValue;

  const UnifiedPercentageField({
    super.key,
    this.controller,
    this.label = 'النسبة المئوية',
    this.hint = '0%',
    this.isRequired = false,
    this.validator,
    this.onChanged,
    this.minValue = 0,
    this.maxValue = 100,
  });

  @override
  Widget build(BuildContext context) {
    return UnifiedTextField(
      controller: controller,
      label: label,
      hint: hint,
      isRequired: isRequired,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      prefixIcon: Icons.percent,
      suffixWidget: Container(
        padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.spacing12,
            vertical: AppDimensions.spacing8),
        child: Text(
          '%',
          style: AppTypography(
            fontSize: AppDimensions.mediumFontSize,
            fontWeight: AppTypography.weightSemiBold,
          ),
        ),
      ),
      validator: validator ?? _defaultPercentageValidator,
      onChanged: (value) {
        final numericValue = double.tryParse(value.replaceAll('%', ''));
        onChanged?.call(numericValue);
      },
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
      ],
    );
  }

  /// التحقق الافتراضي من النسبة المئوية
  String? _defaultPercentageValidator(String? value) {
    if (isRequired && (value == null || value.trim().isEmpty)) {
      return 'النسبة المئوية مطلوبة';
    }

    if (value != null && value.isNotEmpty) {
      final numericValue = double.tryParse(value.replaceAll('%', ''));

      if (numericValue == null) {
        return 'النسبة المئوية غير صحيحة';
      }

      if (numericValue < minValue) {
        return 'النسبة يجب أن تكون أكبر من $minValue%';
      }

      if (numericValue > maxValue) {
        return 'النسبة يجب أن تكون أقل من $maxValue%';
      }
    }

    return null;
  }
}

/// حقل الرمز/الباركود الموحد
class UnifiedCodeField extends StatelessWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String? label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// دالة التحقق المخصصة
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<String>? onChanged;

  /// نوع الرمز (باركود، رقم منتج، إلخ)
  final CodeType codeType;

  /// دالة مسح الرمز
  final VoidCallback? onScanPressed;

  const UnifiedCodeField({
    super.key,
    this.controller,
    this.label,
    this.hint,
    this.isRequired = false,
    this.validator,
    this.onChanged,
    this.codeType = CodeType.barcode,
    this.onScanPressed,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveLabel = label ?? _getDefaultLabel();
    final effectiveHint = hint ?? _getDefaultHint();

    return UnifiedTextField(
      controller: controller,
      label: effectiveLabel,
      hint: effectiveHint,
      isRequired: isRequired,
      keyboardType: TextInputType.text,
      prefixIcon: _getIconForCodeType(),
      suffixIcon: onScanPressed != null ? Icons.qr_code_scanner : null,
      onSuffixIconPressed: onScanPressed,
      validator: validator ?? _defaultCodeValidator,
      onChanged: onChanged,
      inputFormatters: _getFormattersForCodeType(),
    );
  }

  /// الحصول على التسمية الافتراضية
  String _getDefaultLabel() {
    switch (codeType) {
      case CodeType.barcode:
        return 'الباركود';
      case CodeType.productCode:
        return 'رمز المنتج';
      case CodeType.sku:
        return 'رقم الصنف';
      case CodeType.serialNumber:
        return 'الرقم التسلسلي';
    }
  }

  /// الحصول على التلميح الافتراضي
  String _getDefaultHint() {
    switch (codeType) {
      case CodeType.barcode:
        return 'أدخل أو امسح الباركود';
      case CodeType.productCode:
        return 'أدخل رمز المنتج';
      case CodeType.sku:
        return 'أدخل رقم الصنف';
      case CodeType.serialNumber:
        return 'أدخل الرقم التسلسلي';
    }
  }

  /// الحصول على الأيقونة المناسبة
  IconData _getIconForCodeType() {
    switch (codeType) {
      case CodeType.barcode:
        return Icons.qr_code;
      case CodeType.productCode:
        return Icons.inventory_2;
      case CodeType.sku:
        return Icons.tag;
      case CodeType.serialNumber:
        return Icons.confirmation_number;
    }
  }

  /// الحصول على منسقات الإدخال
  List<TextInputFormatter> _getFormattersForCodeType() {
    switch (codeType) {
      case CodeType.barcode:
        return [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9A-Za-z]')),
        ];
      case CodeType.productCode:
        return [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9A-Za-z\-_]')),
        ];
      case CodeType.sku:
        return [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9A-Za-z\-_]')),
        ];
      case CodeType.serialNumber:
        return [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9A-Za-z\-_]')),
        ];
    }
  }

  /// التحقق الافتراضي من الرمز
  String? _defaultCodeValidator(String? value) {
    if (isRequired && (value == null || value.trim().isEmpty)) {
      return '${_getDefaultLabel()} مطلوب';
    }

    if (value != null && value.isNotEmpty) {
      switch (codeType) {
        case CodeType.barcode:
          if (value.length < 8) {
            return 'الباركود يجب أن يكون 8 أرقام على الأقل';
          }
          break;
        case CodeType.productCode:
        case CodeType.sku:
        case CodeType.serialNumber:
          if (value.length < 3) {
            return '${_getDefaultLabel()} يجب أن يكون 3 أحرف على الأقل';
          }
          break;
      }
    }

    return null;
  }
}

/// أنواع الرموز المدعومة
enum CodeType {
  barcode,
  productCode,
  sku,
  serialNumber,
}

// ========== ودجات التاريخ والوقت المتقدمة ==========

/// منتقي التاريخ والوقت الموحد المتقدم
class UnifiedDateTimePicker extends StatefulWidget {
  /// التاريخ والوقت المحدد
  final DateTime? selectedDateTime;

  /// دالة تنفذ عند تغيير التاريخ والوقت
  final ValueChanged<DateTime?> onDateTimeChanged;

  /// تسمية الحقل
  final String? label;

  /// نص التلميح
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// هل يتم عرض منتقي الوقت
  final bool showTimePicker;

  /// هل يتم عرض منتقي التاريخ
  final bool showDatePicker;

  /// أول تاريخ متاح
  final DateTime? firstDate;

  /// آخر تاريخ متاح
  final DateTime? lastDate;

  /// تنسيق عرض التاريخ والوقت
  final String? dateTimeFormat;

  const UnifiedDateTimePicker({
    super.key,
    this.selectedDateTime,
    required this.onDateTimeChanged,
    this.label = 'التاريخ والوقت',
    this.hint = 'اختر التاريخ والوقت',
    this.isRequired = false,
    this.showTimePicker = true,
    this.showDatePicker = true,
    this.firstDate,
    this.lastDate,
    this.dateTimeFormat,
  });

  @override
  State<UnifiedDateTimePicker> createState() => _UnifiedDateTimePickerState();
}

class _UnifiedDateTimePickerState extends State<UnifiedDateTimePicker> {
  late TextEditingController _controller;
  late DateFormat _formatter;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _formatter = DateFormat(widget.dateTimeFormat ?? 'yyyy/MM/dd HH:mm', 'ar');
    _updateController();
  }

  @override
  void didUpdateWidget(UnifiedDateTimePicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedDateTime != widget.selectedDateTime) {
      _updateController();
    }
  }

  void _updateController() {
    if (widget.selectedDateTime != null) {
      _controller.text = _formatter.format(widget.selectedDateTime!);
    } else {
      _controller.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedTextField(
      controller: _controller,
      label: widget.label,
      hint: widget.hint,
      isRequired: widget.isRequired,
      readOnly: true,
      prefixIcon: Icons.calendar_today,
      suffixIcon: Icons.access_time,
      onTap: _showDateTimePicker,
      validator: widget.isRequired ? _defaultDateTimeValidator : null,
    );
  }

  /// عرض منتقي التاريخ والوقت
  Future<void> _showDateTimePicker() async {
    if (!mounted) return;

    DateTime? selectedDate;
    TimeOfDay? selectedTime;

    // اختيار التاريخ
    if (widget.showDatePicker) {
      selectedDate = await showDatePicker(
        context: context,
        initialDate: widget.selectedDateTime ?? DateTime.now(),
        firstDate: widget.firstDate ?? DateTime(2000),
        lastDate: widget.lastDate ?? DateTime(2100),
      );

      if (selectedDate == null || !mounted) return;
    } else {
      selectedDate = widget.selectedDateTime ?? DateTime.now();
    }

    // اختيار الوقت
    if (widget.showTimePicker) {
      selectedTime = await showTimePicker(
        context: context,
        initialTime: widget.selectedDateTime != null
            ? TimeOfDay.fromDateTime(widget.selectedDateTime!)
            : TimeOfDay.now(),
      );

      if (selectedTime == null || !mounted) return;
    } else {
      selectedTime = widget.selectedDateTime != null
          ? TimeOfDay.fromDateTime(widget.selectedDateTime!)
          : TimeOfDay.now();
    }

    // دمج التاريخ والوقت
    final newDateTime = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
      selectedTime.hour,
      selectedTime.minute,
    );

    widget.onDateTimeChanged(newDateTime);
  }

  /// التحقق الافتراضي من التاريخ والوقت
  String? _defaultDateTimeValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '${widget.label} مطلوب';
    }
    return null;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

// ========== الجداول الموحدة ==========

/// جدول البيانات الموحد والذكي
/// يدعم جميع أنواع الجداول مع تصميم موحد ومرن
class UnifiedDataTable<T> extends StatefulWidget {
  /// البيانات المراد عرضها
  final List<T> data;

  /// تعريف الأعمدة
  final List<UnifiedTableColumn<T>> columns;

  /// دالة تنفذ عند النقر على صف
  final Function(T item, int index)? onRowTap;

  /// دالة تنفذ عند ترتيب الجدول
  final Function(int columnIndex, bool ascending)? onSort;

  /// هل يتم عرض مؤشر التحميل
  final bool isLoading;

  /// رسالة عندما تكون البيانات فارغة
  final String emptyMessage;

  /// هل يتم عرض حدود الخلايا
  final bool showBorders;

  /// هل يتم عرض الصفوف بألوان متناوبة
  final bool alternatingRows;

  /// هل يتم عرض أرقام الصفوف
  final bool showRowNumbers;

  /// هل يتم عرض صف الإجماليات
  final bool showTotals;

  /// ارتفاع الجدول المحدد
  final double? height;

  /// عرض الجدول المحدد
  final double? width;

  /// هل يتم تمكين التمرير الأفقي
  final bool horizontalScrollEnabled;

  /// هل يتم تمكين التمرير العمودي
  final bool verticalScrollEnabled;

  /// نمط الجدول
  final TableStyle style;

  /// دالة تصفية مخصصة
  final bool Function(T item, String query)? customFilter;

  /// نص البحث
  final String? searchQuery;

  /// هل يتم عرض شريط البحث
  final bool showSearchBar;

  /// تلميح شريط البحث
  final String searchHint;

  const UnifiedDataTable({
    super.key,
    required this.data,
    required this.columns,
    this.onRowTap,
    this.onSort,
    this.isLoading = false,
    this.emptyMessage = 'لا توجد بيانات',
    this.showBorders = true,
    this.alternatingRows = true,
    this.showRowNumbers = false,
    this.showTotals = false,
    this.height,
    this.width,
    this.horizontalScrollEnabled = true,
    this.verticalScrollEnabled = true,
    this.style = TableStyle.modern,
    this.customFilter,
    this.searchQuery,
    this.showSearchBar = false,
    this.searchHint = 'بحث...',
  });

  @override
  State<UnifiedDataTable<T>> createState() => _UnifiedDataTableState<T>();
}

class _UnifiedDataTableState<T> extends State<UnifiedDataTable<T>> {
  late TextEditingController _searchController;
  List<T> _filteredData = [];
  int? _sortColumnIndex;
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.searchQuery);
    _filteredData = List.from(widget.data);
    _applyFilter();
  }

  @override
  void didUpdateWidget(UnifiedDataTable<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data != widget.data ||
        oldWidget.searchQuery != widget.searchQuery) {
      _filteredData = List.from(widget.data);
      _searchController.text = widget.searchQuery ?? '';
      _applyFilter();
    }
  }

  void _applyFilter() {
    if (_searchController.text.isEmpty) {
      _filteredData = List.from(widget.data);
    } else {
      _filteredData = widget.data.where((item) {
        if (widget.customFilter != null) {
          return widget.customFilter!(item, _searchController.text);
        }
        // فلترة افتراضية بناءً على toString
        return item
            .toString()
            .toLowerCase()
            .contains(_searchController.text.toLowerCase());
      }).toList();
    }

    // تطبيق الترتيب إذا كان موجوداً
    if (_sortColumnIndex != null) {
      _applySorting(_sortColumnIndex!, _sortAscending);
    }

    setState(() {});
  }

  void _applySorting(int columnIndex, bool ascending) {
    final column = widget.columns[columnIndex];
    if (column.sortable && column.getValue != null) {
      _filteredData.sort((a, b) {
        final valueA = column.getValue!(a);
        final valueB = column.getValue!(b);

        int comparison = 0;
        if (valueA is Comparable && valueB is Comparable) {
          comparison = valueA.compareTo(valueB);
        } else {
          comparison = valueA.toString().compareTo(valueB.toString());
        }

        return ascending ? comparison : -comparison;
      });
    }

    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;
    });

    widget.onSort?.call(columnIndex, ascending);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // شريط البحث
        if (widget.showSearchBar) ...[
          Padding(
            padding: const EdgeInsets.all(16),
            child: UnifiedTextField.search(
              controller: _searchController,
              hint: widget.searchHint,
              onChanged: (_) => _applyFilter(),
            ),
          ),
        ],

        // الجدول
        Expanded(
          child: _buildTable(),
        ),
      ],
    );
  }

  Widget _buildTable() {
    if (widget.isLoading) {
      return const Center(
        child: UnifiedLoadingIndicator(
          message: 'جاري تحميل البيانات...',
        ),
      );
    }

    if (_filteredData.isEmpty) {
      return UnifiedEmptyState(
        message: widget.emptyMessage,
        icon: Icons.table_chart,
      );
    }

    return Container(
      height: widget.height,
      width: widget.width,
      decoration: _getTableDecoration(),
      child: _buildScrollableTable(),
    );
  }

  BoxDecoration _getTableDecoration() {
    switch (widget.style) {
      case TableStyle.modern:
        return BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.getAdaptiveBorderColor(
                Theme.of(context).brightness == Brightness.dark),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        );
      case TableStyle.classic:
        return BoxDecoration(
          border: Border.all(
            color: AppColors.getAdaptiveBorderColor(
                Theme.of(context).brightness == Brightness.dark),
            width: 1,
          ),
        );
      case TableStyle.minimal:
        return const BoxDecoration();
    }
  }

  Widget _buildScrollableTable() {
    Widget table = _buildDataTable();

    if (widget.horizontalScrollEnabled) {
      table = SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: table,
      );
    }

    if (widget.verticalScrollEnabled) {
      table = SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: table,
      );
    }

    return table;
  }

  Widget _buildDataTable() {
    final columns = _buildColumns();
    final rows = _buildRows();

    return DataTable(
      columns: columns,
      rows: rows,
      border: widget.showBorders ? _getTableBorder() : null,
      headingRowColor: _getHeaderColor(),
      dataRowColor: widget.alternatingRows ? _getAlternatingRowColor() : null,
      headingTextStyle: _getHeaderTextStyle(),
      dataTextStyle: _getDataTextStyle(),
      columnSpacing: 24,
      horizontalMargin: 16,
      headingRowHeight: 56,
      dataRowMinHeight: 48,
      dataRowMaxHeight: 48,
      sortColumnIndex: _sortColumnIndex,
      sortAscending: _sortAscending,
    );
  }

  List<DataColumn> _buildColumns() {
    List<DataColumn> columns = [];

    // عمود أرقام الصفوف
    if (widget.showRowNumbers) {
      columns.add(
        const DataColumn(
          label: Text('#'),
          numeric: true,
        ),
      );
    }

    // الأعمدة الأساسية
    for (int i = 0; i < widget.columns.length; i++) {
      final column = widget.columns[i];
      columns.add(
        DataColumn(
          label: Text(column.title),
          tooltip: column.tooltip,
          numeric: column.numeric,
          onSort: column.sortable
              ? (columnIndex, ascending) {
                  _applySorting(i, ascending);
                }
              : null,
        ),
      );
    }

    return columns;
  }

  List<DataRow> _buildRows() {
    List<DataRow> rows = [];

    // صفوف البيانات
    for (int i = 0; i < _filteredData.length; i++) {
      final item = _filteredData[i];
      final cells = <DataCell>[];

      // خلية رقم الصف
      if (widget.showRowNumbers) {
        cells.add(DataCell(Text('${i + 1}')));
      }

      // خلايا البيانات
      for (final column in widget.columns) {
        final value = column.getValue?.call(item);

        if (column.editable && column.buildCell == null) {
          // خلية قابلة للتحرير
          cells.add(_buildEditableCell(item, column, value, i));
        } else {
          // خلية عادية أو مخصصة
          final cellWidget = column.buildCell?.call(item, value) ??
              DataCell(Text(value?.toString() ?? ''));
          cells.add(cellWidget);
        }
      }

      rows.add(
        DataRow(
          cells: cells,
          onSelectChanged:
              widget.onRowTap != null ? (_) => widget.onRowTap!(item, i) : null,
        ),
      );
    }

    // صف الإجماليات
    if (widget.showTotals) {
      rows.add(_buildTotalsRow());
    }

    return rows;
  }

  DataRow _buildTotalsRow() {
    final cells = <DataCell>[];

    // خلية رقم الصف للإجماليات
    if (widget.showRowNumbers) {
      cells.add(const DataCell(Text('المجموع',
          style: AppTypography(fontWeight: AppTypography.weightBold))));
    }

    // خلايا الإجماليات
    for (int i = 0; i < widget.columns.length; i++) {
      final column = widget.columns[i];

      if (column.showTotal && column.getValue != null) {
        // حساب المجموع
        double total = 0;
        for (final item in _filteredData) {
          final value = column.getValue!(item);
          if (value is num) {
            total += value.toDouble();
          }
        }

        cells.add(DataCell(
          Text(
            column.formatTotal?.call(total) ?? total.toStringAsFixed(2),
            style: const AppTypography(fontWeight: AppTypography.weightBold),
          ),
        ));
      } else if (i == 0 && !widget.showRowNumbers) {
        // العمود الأول يعرض "المجموع"
        cells.add(const DataCell(Text('المجموع',
            style: AppTypography(fontWeight: AppTypography.weightBold))));
      } else {
        // خلية فارغة
        cells.add(const DataCell(SizedBox()));
      }
    }

    return DataRow(
      cells: cells,
      color: WidgetStateProperty.all(
        AppColors.primary.withValues(alpha: 0.1),
      ),
    );
  }

  // دوال مساعدة للتنسيق
  TableBorder _getTableBorder() {
    final borderColor = AppColors.getAdaptiveBorderColor(
        Theme.of(context).brightness == Brightness.dark);

    return TableBorder.all(
      color: borderColor,
      width: AppDimensions.spacing2 / 4, // 0.5
    );
  }

  WidgetStateProperty<Color?> _getHeaderColor() {
    return WidgetStateProperty.all(
      AppColors.primary.withValues(alpha: 0.1),
    );
  }

  WidgetStateProperty<Color?> _getAlternatingRowColor() {
    return WidgetStateProperty.resolveWith<Color?>((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.primary.withValues(alpha: 0.2);
      }
      return null; // سيتم التعامل مع الألوان المتناوبة في buildRows
    });
  }

  TextStyle _getHeaderTextStyle() {
    return AppTypography(
      fontWeight: AppTypography.weightBold,
      fontSize: AppDimensions.defaultFontSize,
    );
  }

  TextStyle _getDataTextStyle() {
    return AppTypography(
      fontSize: AppDimensions.smallFontSize,
    );
  }

  /// بناء خلية قابلة للتحرير
  DataCell _buildEditableCell(
      dynamic item, UnifiedTableColumn column, dynamic value, int rowIndex) {
    switch (column.editType) {
      case EditableFieldType.text:
        return _buildTextEditCell(item, column, value, rowIndex);

      case EditableFieldType.number:
        return _buildNumberEditCell(item, column, value, rowIndex);

      case EditableFieldType.currency:
        return _buildCurrencyEditCell(item, column, value, rowIndex);

      case EditableFieldType.percentage:
        return _buildPercentageEditCell(item, column, value, rowIndex);

      case EditableFieldType.dropdown:
        return _buildDropdownEditCell(item, column, value, rowIndex);

      case EditableFieldType.date:
        return _buildDateEditCell(item, column, value, rowIndex);

      case EditableFieldType.dateTime:
        return _buildDateTimeEditCell(item, column, value, rowIndex);

      case EditableFieldType.phone:
        return _buildPhoneEditCell(item, column, value, rowIndex);

      case EditableFieldType.email:
        return _buildEmailEditCell(item, column, value, rowIndex);

      case EditableFieldType.multiline:
        return _buildMultilineEditCell(item, column, value, rowIndex);
    }
  }

  /// بناء خلية تحرير نص
  DataCell _buildTextEditCell(
      dynamic item, UnifiedTableColumn column, dynamic value, int rowIndex) {
    return DataCell(
      Container(
        constraints: BoxConstraints(
            minWidth: AppDimensions.buttonWidthSmall + AppDimensions.spacing20),
        child: TextFormField(
          initialValue: value?.toString() ?? '',
          style: AppTypography(fontSize: AppDimensions.smallFontSize),
          decoration: const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(
                horizontal: AppDimensions.spacing8,
                vertical: AppDimensions.spacing4),
            isDense: true,
          ),
          onChanged: (newValue) {
            column.onValueChanged?.call(item, newValue);
          },
          validator:
              column.validator != null ? (val) => column.validator!(val) : null,
        ),
      ),
    );
  }

  /// بناء خلية تحرير رقم
  DataCell _buildNumberEditCell(
      dynamic item, UnifiedTableColumn column, dynamic value, int rowIndex) {
    return DataCell(
      Container(
        constraints: BoxConstraints(minWidth: AppDimensions.buttonWidthSmall),
        child: TextFormField(
          initialValue: value?.toString() ?? '',
          style: AppTypography(fontSize: AppDimensions.smallFontSize),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          decoration: const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(
                horizontal: AppDimensions.spacing8,
                vertical: AppDimensions.spacing4),
            isDense: true,
          ),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
          ],
          onChanged: (newValue) {
            final numValue = double.tryParse(newValue);
            if (numValue != null) {
              // التحقق من النطاق
              if (column.minValue != null && numValue < column.minValue!) {
                return;
              }
              if (column.maxValue != null && numValue > column.maxValue!) {
                return;
              }

              column.onValueChanged?.call(item, numValue);
            }
          },
          validator: column.validator != null
              ? (val) => column.validator!(val)
              : (val) {
                  if (val != null && val.isNotEmpty) {
                    final numValue = double.tryParse(val);
                    if (numValue == null) return 'رقم غير صحيح';
                    if (column.minValue != null &&
                        numValue < column.minValue!) {
                      return 'القيمة يجب أن تكون أكبر من ${column.minValue}';
                    }
                    if (column.maxValue != null &&
                        numValue > column.maxValue!) {
                      return 'القيمة يجب أن تكون أقل من ${column.maxValue}';
                    }
                  }
                  return null;
                },
        ),
      ),
    );
  }

  /// بناء خلية تحرير مبلغ مالي
  DataCell _buildCurrencyEditCell(
      dynamic item, UnifiedTableColumn column, dynamic value, int rowIndex) {
    return DataCell(
      Container(
        constraints: BoxConstraints(minWidth: AppDimensions.buttonWidthMedium),
        child: TextFormField(
          initialValue:
              value != null ? NumberFormat('#,##0.00').format(value) : '',
          style: AppTypography(
              fontSize: AppDimensions.smallFontSize,
              fontWeight: AppTypography.weightSemiBold),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          decoration: const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(
                horizontal: AppDimensions.spacing8,
                vertical: AppDimensions.spacing4),
            isDense: true,
            suffixText: 'ر.ي',
          ),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
          ],
          onChanged: (newValue) {
            final cleanValue = newValue.replaceAll(',', '');
            final numValue = double.tryParse(cleanValue);
            if (numValue != null) {
              column.onValueChanged?.call(item, numValue);
            }
          },
        ),
      ),
    );
  }

  /// بناء خلية تحرير نسبة مئوية
  DataCell _buildPercentageEditCell<T>(
      T item, UnifiedTableColumn<T> column, dynamic value, int rowIndex) {
    return DataCell(
      Container(
        constraints: const BoxConstraints(minWidth: 80),
        child: TextFormField(
          initialValue: value?.toString() ?? '',
          style: const AppTypography(fontSize: 13),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          decoration: const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            isDense: true,
            suffixText: '%',
          ),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
          ],
          onChanged: (newValue) {
            final numValue = double.tryParse(newValue);
            if (numValue != null && numValue >= 0 && numValue <= 100) {
              column.onValueChanged?.call(item, numValue);
            }
          },
        ),
      ),
    );
  }

  /// بناء خلية قائمة منسدلة
  DataCell _buildDropdownEditCell<T>(
      T item, UnifiedTableColumn<T> column, dynamic value, int rowIndex) {
    return DataCell(
      Container(
        constraints: const BoxConstraints(minWidth: 120),
        child: DropdownButtonFormField<dynamic>(
          value: value,
          style: const AppTypography(fontSize: 13),
          decoration: const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            isDense: true,
          ),
          items: column.dropdownOptions?.map((option) {
            return DropdownMenuItem<dynamic>(
              value: option.value,
              child: Text(option.label),
            );
          }).toList(),
          onChanged: (newValue) {
            column.onValueChanged?.call(item, newValue);
          },
        ),
      ),
    );
  }

  /// بناء خلية تحرير تاريخ
  DataCell _buildDateEditCell<T>(
      T item, UnifiedTableColumn<T> column, dynamic value, int rowIndex) {
    return DataCell(
      Container(
        constraints: const BoxConstraints(minWidth: 120),
        child: TextFormField(
          initialValue: value is DateTime
              ? DateFormat('yyyy/MM/dd').format(value)
              : value?.toString() ?? '',
          style: const AppTypography(fontSize: 13),
          readOnly: true,
          decoration: const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            isDense: true,
            suffixIcon: Icon(Icons.calendar_today, size: 16),
          ),
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: value is DateTime ? value : DateTime.now(),
              firstDate: DateTime(2000),
              lastDate: DateTime(2100),
            );
            if (date != null) {
              column.onValueChanged?.call(item, date);
            }
          },
        ),
      ),
    );
  }

  /// بناء خلية تحرير تاريخ ووقت
  DataCell _buildDateTimeEditCell<T>(
      T item, UnifiedTableColumn<T> column, dynamic value, int rowIndex) {
    return DataCell(
      Container(
        constraints: const BoxConstraints(minWidth: 150),
        child: TextFormField(
          initialValue: value is DateTime
              ? DateFormat('yyyy/MM/dd HH:mm').format(value)
              : value?.toString() ?? '',
          style: const AppTypography(fontSize: 13),
          readOnly: true,
          decoration: const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            isDense: true,
            suffixIcon: Icon(Icons.access_time, size: 16),
          ),
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: value is DateTime ? value : DateTime.now(),
              firstDate: DateTime(2000),
              lastDate: DateTime(2100),
            );
            if (date != null && mounted) {
              final time = await showTimePicker(
                context: context,
                initialTime: value is DateTime
                    ? TimeOfDay.fromDateTime(value)
                    : TimeOfDay.now(),
              );
              if (time != null) {
                final dateTime = DateTime(
                  date.year,
                  date.month,
                  date.day,
                  time.hour,
                  time.minute,
                );
                column.onValueChanged?.call(item, dateTime);
              }
            }
          },
        ),
      ),
    );
  }

  /// بناء خلية تحرير هاتف
  DataCell _buildPhoneEditCell<T>(
      T item, UnifiedTableColumn<T> column, dynamic value, int rowIndex) {
    return DataCell(
      Container(
        constraints: const BoxConstraints(minWidth: 120),
        child: TextFormField(
          initialValue: value?.toString() ?? '',
          style: const AppTypography(fontSize: 13),
          keyboardType: TextInputType.phone,
          decoration: const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            isDense: true,
            prefixText: '+966 ',
          ),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9\s\-\(\)]')),
          ],
          onChanged: (newValue) {
            column.onValueChanged?.call(item, newValue);
          },
        ),
      ),
    );
  }

  /// بناء خلية تحرير بريد إلكتروني
  DataCell _buildEmailEditCell<T>(
      T item, UnifiedTableColumn<T> column, dynamic value, int rowIndex) {
    return DataCell(
      Container(
        constraints: const BoxConstraints(minWidth: 150),
        child: TextFormField(
          initialValue: value?.toString() ?? '',
          style: const AppTypography(fontSize: 13),
          keyboardType: TextInputType.emailAddress,
          decoration: const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            isDense: true,
          ),
          onChanged: (newValue) {
            column.onValueChanged?.call(item, newValue);
          },
          validator: column.validator != null
              ? (val) => column.validator!(val)
              : (val) {
                  if (val != null && val.isNotEmpty) {
                    final emailRegExp = RegExp(
                        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
                    if (!emailRegExp.hasMatch(val)) {
                      return 'بريد إلكتروني غير صحيح';
                    }
                  }
                  return null;
                },
        ),
      ),
    );
  }

  /// بناء خلية تحرير نص متعدد الأسطر
  DataCell _buildMultilineEditCell<T>(
      T item, UnifiedTableColumn<T> column, dynamic value, int rowIndex) {
    return DataCell(
      Container(
        constraints: BoxConstraints(
            minWidth: AppDimensions.cardWidthLarge,
            minHeight: AppDimensions.cardHeightTiny),
        child: TextFormField(
          initialValue: value?.toString() ?? '',
          style: AppTypography(fontSize: AppDimensions.smallFontSize),
          maxLines: 3,
          decoration: const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(
                horizontal: AppDimensions.spacing8,
                vertical: AppDimensions.spacing4),
            isDense: true,
          ),
          onChanged: (newValue) {
            column.onValueChanged?.call(item, newValue);
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

/// تعريف عمود في الجدول الموحد
class UnifiedTableColumn<T> {
  /// عنوان العمود
  final String title;

  /// تلميح العمود
  final String? tooltip;

  /// هل العمود رقمي
  final bool numeric;

  /// هل يمكن ترتيب العمود
  final bool sortable;

  /// هل يتم عرض مجموع العمود
  final bool showTotal;

  /// دالة للحصول على قيمة العمود
  final dynamic Function(T item)? getValue;

  /// دالة لبناء خلية مخصصة
  final DataCell Function(T item, dynamic value)? buildCell;

  /// دالة لتنسيق المجموع
  final String Function(double total)? formatTotal;

  /// عرض العمود
  final double? width;

  /// هل يمكن تحرير هذا العمود
  final bool editable;

  /// نوع حقل الإدخال للتحرير
  final EditableFieldType editType;

  /// دالة تنفذ عند تغيير القيمة
  final Function(T item, dynamic newValue)? onValueChanged;

  /// دالة التحقق من صحة القيمة
  final String? Function(dynamic value)? validator;

  /// قائمة الخيارات للقوائم المنسدلة
  final List<DropdownOption>? dropdownOptions;

  /// الحد الأدنى للقيم الرقمية
  final double? minValue;

  /// الحد الأقصى للقيم الرقمية
  final double? maxValue;

  /// عدد الخانات العشرية للأرقام
  final int? decimalPlaces;

  const UnifiedTableColumn({
    required this.title,
    this.tooltip,
    this.numeric = false,
    this.sortable = true,
    this.showTotal = false,
    this.getValue,
    this.buildCell,
    this.formatTotal,
    this.width,
    this.editable = false,
    this.editType = EditableFieldType.text,
    this.onValueChanged,
    this.validator,
    this.dropdownOptions,
    this.minValue,
    this.maxValue,
    this.decimalPlaces,
  });
}

/// أنواع حقول الإدخال المدعومة للتحرير
enum EditableFieldType {
  text, // نص عادي
  number, // رقم
  currency, // مبلغ مالي
  percentage, // نسبة مئوية
  dropdown, // قائمة منسدلة
  date, // تاريخ
  dateTime, // تاريخ ووقت
  phone, // رقم هاتف
  email, // بريد إلكتروني
  multiline, // نص متعدد الأسطر
}

/// خيار في القائمة المنسدلة
class DropdownOption {
  final String label;
  final dynamic value;

  const DropdownOption({
    required this.label,
    required this.value,
  });
}

/// أنماط الجداول المتاحة
enum TableStyle {
  modern, // عصري مع حواف مدورة وظلال
  classic, // كلاسيكي مع حدود بسيطة
  minimal, // بسيط بدون حدود
}

// ========== حقول النصوص الموحدة ==========

/// حقل النص الموحد
/// يدعم جميع أنواع حقول النصوص مع تصميم موحد
class UnifiedTextField extends StatelessWidget {
  /// وحدة التحكم في النص
  final TextEditingController? controller;

  /// تسمية الحقل
  final String? label;

  /// نص التلميح
  final String? hint;

  /// نص الخطأ
  final String? errorText;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// هل النص مخفي (لكلمات المرور)
  final bool obscureText;

  /// هل الحقل للقراءة فقط
  final bool readOnly;

  /// هل الحقل مفعل
  final bool enabled;

  /// نوع لوحة المفاتيح
  final TextInputType keyboardType;

  /// إجراء لوحة المفاتيح
  final TextInputAction textInputAction;

  /// عدد الأسطر الأقصى
  final int? maxLines;

  /// عدد الأسطر الأدنى
  final int? minLines;

  /// الحد الأقصى لعدد الأحرف
  final int? maxLength;

  /// دالة التحقق من صحة المدخلات
  final String? Function(String?)? validator;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<String>? onChanged;

  /// دالة تنفذ عند الضغط على الحقل
  final VoidCallback? onTap;

  /// أيقونة في بداية الحقل
  final IconData? prefixIcon;

  /// ودجت في بداية الحقل
  final Widget? prefixWidget;

  /// أيقونة في نهاية الحقل
  final IconData? suffixIcon;

  /// ودجت في نهاية الحقل
  final Widget? suffixWidget;

  /// دالة تنفذ عند الضغط على أيقونة النهاية
  final VoidCallback? onSuffixIconPressed;

  /// منسقات الإدخال
  final List<TextInputFormatter>? inputFormatters;

  /// المسافة الداخلية للمحتوى
  final EdgeInsetsGeometry? contentPadding;

  /// نقطة التركيز
  final FocusNode? focusNode;

  /// هل يتم التركيز تلقائياً
  final bool autofocus;

  /// محاذاة النص
  final TextAlign textAlign;

  const UnifiedTextField({
    super.key,
    this.controller,
    this.label,
    this.hint,
    this.errorText,
    this.isRequired = false,
    this.obscureText = false,
    this.readOnly = false,
    this.enabled = true,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.validator,
    this.onChanged,
    this.onTap,
    this.prefixIcon,
    this.prefixWidget,
    this.suffixIcon,
    this.suffixWidget,
    this.onSuffixIconPressed,
    this.inputFormatters,
    this.contentPadding,
    this.focusNode,
    this.autofocus = false,
    this.textAlign = TextAlign.start,
  });

  /// مُنشئ مبسط لحقل البحث
  const UnifiedTextField.search({
    super.key,
    required this.controller,
    this.hint = 'بحث...',
    this.onChanged,
    VoidCallback? onClear,
  })  : label = null,
        errorText = null,
        isRequired = false,
        obscureText = false,
        readOnly = false,
        enabled = true,
        keyboardType = TextInputType.text,
        textInputAction = TextInputAction.search,
        maxLines = 1,
        minLines = null,
        maxLength = null,
        validator = null,
        onTap = null,
        prefixIcon = Icons.search,
        prefixWidget = null,
        suffixIcon = Icons.clear,
        suffixWidget = null,
        onSuffixIconPressed = onClear,
        inputFormatters = null,
        contentPadding = null,
        focusNode = null,
        autofocus = false,
        textAlign = TextAlign.start;

  /// مُنشئ مبسط لحقل كلمة المرور
  const UnifiedTextField.password({
    super.key,
    required this.controller,
    this.label = 'كلمة المرور',
    this.hint,
    this.isRequired = true,
    this.validator,
    this.onChanged,
  })  : errorText = null,
        obscureText = true,
        readOnly = false,
        enabled = true,
        keyboardType = TextInputType.visiblePassword,
        textInputAction = TextInputAction.done,
        maxLines = 1,
        minLines = null,
        maxLength = null,
        onTap = null,
        prefixIcon = Icons.lock_outline,
        prefixWidget = null,
        suffixIcon = Icons.visibility,
        suffixWidget = null,
        onSuffixIconPressed = null,
        inputFormatters = null,
        contentPadding = null,
        focusNode = null,
        autofocus = false,
        textAlign = TextAlign.start;

  /// مُنشئ مبسط لحقل الأرقام
  const UnifiedTextField.number({
    super.key,
    required this.controller,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.validator,
    this.onChanged,
  })  : errorText = null,
        obscureText = false,
        readOnly = false,
        enabled = true,
        keyboardType = TextInputType.number,
        textInputAction = TextInputAction.next,
        maxLines = 1,
        minLines = null,
        maxLength = null,
        onTap = null,
        prefixIcon = Icons.numbers,
        prefixWidget = null,
        suffixIcon = null,
        suffixWidget = null,
        onSuffixIconPressed = null,
        inputFormatters = null,
        contentPadding = null,
        focusNode = null,
        autofocus = false,
        textAlign = TextAlign.start;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // تسمية الحقل
        if (label != null && label!.isNotEmpty) ...[
          Text(
            isRequired ? '$label *' : label!,
            style: AppTypography(
              fontSize: AppDimensions.smallFontSize,
              fontWeight: AppTypography.weightSemiBold,
              color: AppColors.getAdaptiveTextColor(isDark),
            ),
          ),
          const SizedBox(height: AppDimensions.spacing8),
        ],

        // حقل النص
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            labelText: label == null ? null : (isRequired ? '$label *' : label),
            hintText: hint,
            errorText: errorText,
            prefixIcon: prefixIcon != null ? Icon(prefixIcon) : prefixWidget,
            suffixIcon: _buildSuffixIcon(),
            contentPadding: contentPadding ??
                const EdgeInsets.symmetric(
                    horizontal: AppDimensions.spacing16,
                    vertical: AppDimensions.spacing12),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              borderSide: BorderSide(
                color: AppColors.getAdaptiveBorderColor(isDark),
                width: AppDimensions.spacing2 / 2, // 1.0
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: AppDimensions.spacing2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              borderSide: const BorderSide(
                color: AppColors.error,
                width: AppDimensions.spacing2 / 2, // 1.0
              ),
            ),
            filled: true,
            fillColor: isDark
                ? AppColors.darkSurfaceVariant
                : AppColors.lightSurfaceVariant,
          ),
          obscureText: obscureText,
          readOnly: readOnly,
          enabled: enabled,
          keyboardType: keyboardType,
          textInputAction: textInputAction,
          maxLines: maxLines,
          minLines: minLines,
          maxLength: maxLength,
          validator: validator ?? (isRequired ? _defaultValidator : null),
          onChanged: onChanged,
          onTap: onTap,
          inputFormatters: inputFormatters,
          focusNode: focusNode,
          autofocus: autofocus,
          textAlign: textAlign,
          style: AppTypography(
            fontSize: AppDimensions.defaultFontSize,
            color: AppColors.getAdaptiveTextColor(isDark),
          ),
        ),
      ],
    );
  }

  /// بناء أيقونة النهاية
  Widget? _buildSuffixIcon() {
    if (suffixWidget != null) return suffixWidget;

    if (suffixIcon != null) {
      return IconButton(
        icon: Icon(suffixIcon),
        onPressed: onSuffixIconPressed,
      );
    }

    return null;
  }

  /// التحقق الافتراضي للحقول المطلوبة
  String? _defaultValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'هذا الحقل مطلوب';
    }
    return null;
  }
}

// ========== حالات الواجهة الموحدة ==========

/// ودجت الحالة الفارغة الموحد
/// يعرض رسالة ورمز عندما لا توجد بيانات
class UnifiedEmptyState extends StatelessWidget {
  /// الرسالة المعروضة
  final String message;

  /// الأيقونة المعروضة
  final IconData icon;

  /// نص الزر الاختياري
  final String? buttonText;

  /// دالة الزر الاختياري
  final VoidCallback? onButtonPressed;

  /// دالة التحديث (للسحب للتحديث)
  final VoidCallback? onRefresh;

  /// لون الأيقونة والنص
  final Color? color;

  const UnifiedEmptyState({
    super.key,
    required this.message,
    this.icon = Icons.inbox_outlined,
    this.buttonText,
    this.onButtonPressed,
    this.onRefresh,
    this.color,
  });

  /// مُنشئ مبسط لحالة عدم وجود بيانات
  const UnifiedEmptyState.noData({
    super.key,
    this.message = 'لا توجد بيانات',
    this.buttonText,
    this.onButtonPressed,
    this.onRefresh,
  })  : icon = Icons.inbox_outlined,
        color = null;

  /// مُنشئ مبسط لحالة عدم وجود نتائج بحث
  const UnifiedEmptyState.noSearchResults({
    super.key,
    this.message = 'لا توجد نتائج للبحث',
    this.onRefresh,
  })  : icon = Icons.search_off,
        buttonText = null,
        onButtonPressed = null,
        color = null;

  /// مُنشئ مبسط لحالة الخطأ
  const UnifiedEmptyState.error({
    super.key,
    this.message = 'حدث خطأ أثناء تحميل البيانات',
    this.buttonText = 'إعادة المحاولة',
    this.onButtonPressed,
    this.onRefresh,
  })  : icon = Icons.error_outline,
        color = AppColors.error;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final effectiveColor =
        color ?? AppColors.getAdaptiveSecondaryTextColor(isDark);

    Widget content = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // الأيقونة
        Icon(
          icon,
          size: AppDimensions.iconSizeXXLarge,
          color: effectiveColor.withValues(alpha: 0.6),
        ),

        const SizedBox(height: AppDimensions.spacing24),
        // const SizedBox(height: AppDimensions.spacingAppDimensions.spacing24),

        // الرسالة
        Text(
          message,
          textAlign: TextAlign.center,
          style: AppTypography(
            fontSize: AppDimensions.titleFontSize,
            fontWeight: AppTypography.weightMedium,
            color: effectiveColor,
          ),
        ),

        const SizedBox(height: AppDimensions.spacing32),

        // الزر الاختياري
        if (buttonText != null && onButtonPressed != null)
          UnifiedButton(
            text: buttonText!,
            onPressed: onButtonPressed,
            backgroundColor: Theme.of(context).primaryColor,
            icon: Icons.refresh,
          ),
      ],
    );

    // إضافة السحب للتحديث إذا كان متاحاً
    if (onRefresh != null) {
      content = RefreshIndicator(
        onRefresh: () async {
          onRefresh!();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: MediaQuery.of(context).size.height *
                AppDimensions.dialogHeightRatio,
            child: Center(child: content),
          ),
        ),
      );
    }

    return Center(child: content);
  }
}

/// ودجت حالة الخطأ الموحد
/// يعرض رسالة خطأ مع إمكانية إعادة المحاولة
class UnifiedErrorState extends StatelessWidget {
  /// رسالة الخطأ
  final String message;

  /// تفاصيل الخطأ الاختيارية
  final String? details;

  /// دالة إعادة المحاولة
  final VoidCallback? onRetry;

  /// نص زر إعادة المحاولة
  final String retryText;

  const UnifiedErrorState({
    super.key,
    required this.message,
    this.details,
    this.onRetry,
    this.retryText = 'إعادة المحاولة',
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.spacing24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة الخطأ
            const Icon(
              Icons.error_outline,
              size: AppDimensions.iconSizeXXLarge,
              color: AppColors.error,
            ),

            const SizedBox(height: AppDimensions.spacing24),

            // رسالة الخطأ
            Text(
              message,
              textAlign: TextAlign.center,
              style: AppTypography(
                fontSize: AppDimensions.largeFontSize,
                fontWeight: AppTypography.weightSemiBold,
                color: AppColors.error,
              ),
            ),

            // تفاصيل الخطأ
            if (details != null) ...[
              const SizedBox(height: AppDimensions.spacing12),
              Text(
                details!,
                textAlign: TextAlign.center,
                style: AppTypography(
                  fontSize: AppDimensions.defaultFontSize,
                  color: AppColors.getAdaptiveSecondaryTextColor(
                      Theme.of(context).brightness == Brightness.dark),
                ),
              ),
            ],

            const SizedBox(height: AppDimensions.spacing32),

            // زر إعادة المحاولة
            if (onRetry != null)
              UnifiedButton(
                text: retryText,
                onPressed: onRetry,
                backgroundColor: AppColors.error,
                icon: Icons.refresh,
              ),
          ],
        ),
      ),
    );
  }
}
