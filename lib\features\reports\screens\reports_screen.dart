import 'package:flutter/material.dart';

import '../../../core/utils/index.dart';
import '../../../core/widgets/index.dart'; // استخدام الودجات الموحدة
import 'top_selling_products_report_screen.dart';
import 'most_profitable_products_report_screen.dart';
import 'sales_report_screen.dart';
import '../../warehouses/screens/inventory_report_screen.dart';
import '../../warehouses/screens/low_stock_report_screen.dart';
import '../../warehouses/screens/inventory_movement_report_screen.dart';
import '../../../core/theme/index.dart';

/// شاشة التقارير الرئيسية
///
/// تعرض هذه الشاشة قائمة بالتقارير المتاحة في النظام
class ReportsScreen extends StatelessWidget {
  const ReportsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const UnifiedAppBar(
        title: 'التقارير',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تقارير المبيعات',
              style: AppTypography(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildReportGrid(
              context,
              [
                _ReportItem(
                  title: 'تقرير المبيعات',
                  icon: Icons.trending_up,
                  color: AppColors.success,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SalesReportScreen(),
                    ),
                  ),
                ),
                _ReportItem(
                  title: 'المنتجات الأكثر مبيعًا',
                  icon: Icons.star,
                  color: AppColors.accent,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          const TopSellingProductsReportScreen(),
                    ),
                  ),
                ),
                _ReportItem(
                  title: 'المنتجات الأكثر ربحية',
                  icon: Icons.attach_money,
                  color: AppColors.info,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          const MostProfitableProductsReportScreen(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            const Text(
              'تقارير المخزون',
              style: AppTypography(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildReportGrid(
              context,
              [
                _ReportItem(
                  title: 'تقرير المخزون',
                  icon: Icons.inventory,
                  color: AppColors.primary,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const InventoryReportScreen(),
                    ),
                  ),
                ),
                _ReportItem(
                  title: 'المنتجات منخفضة المخزون',
                  icon: Icons.warning_amber,
                  color: AppColors.warning,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const LowStockReportScreen(),
                    ),
                  ),
                ),
                _ReportItem(
                  title: 'حركة المخزون',
                  icon: Icons.timeline,
                  color: AppColors.secondary,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          const InventoryMovementReportScreen(),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportGrid(BuildContext context, List<_ReportItem> items) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: Layout.isMobile() ? 2 : 3,
        childAspectRatio: 1.2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildReportCard(
          context,
          title: item.title,
          icon: item.icon,
          color: item.color,
          onTap: item.onTap,
        );
      },
    );
  }

  Widget _buildReportCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.defaultMargin),
          child: LayoutBuilder(
            builder: (context, constraints) {
              // تحديد الأحجام بناءً على المساحة المتاحة
              final availableHeight = constraints.maxHeight;
              final iconSize = availableHeight > 100
                  ? AppDimensions.largeIconSize
                  : AppDimensions.mediumIconSize;
              final fontSize = availableHeight > 100
                  ? AppDimensions.defaultFontSize
                  : AppDimensions.smallFontSize;
              final spacing = availableHeight > 100
                  ? AppDimensions.defaultSpacing
                  : AppDimensions.tinySpacing;

              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    icon,
                    size: iconSize,
                    color: color,
                  ),
                  SizedBox(height: spacing),
                  Flexible(
                    child: Text(
                      title,
                      style: AppTypography(
                        fontSize: fontSize,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: availableHeight > 100 ? 2 : 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}

class _ReportItem {
  final String title;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  _ReportItem({
    required this.title,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}
