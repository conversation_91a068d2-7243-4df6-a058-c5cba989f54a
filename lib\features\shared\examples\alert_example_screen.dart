import 'package:flutter/material.dart';
import '../../../core/utils/index.dart';
import '../../../core/widgets/index.dart'; // استخدام الودجات الموحدة الجديدة
import '../../../core/theme/index.dart';

/// شاشة مثال لاستخدام نظام التنبيهات
class AlertExampleScreen extends StatefulWidget {
  const AlertExampleScreen({Key? key}) : super(key: key);

  @override
  State<AlertExampleScreen> createState() => _AlertExampleScreenState();
}

class _AlertExampleScreenState extends State<AlertExampleScreen> {
  @override
  Widget build(BuildContext context) {
    // تهيئة أدوات التخطيط
    Layout.init(context);

    return Scaffold(
      appBar: const UnifiedAppBar(
        title: 'أمثلة التنبيهات',
      ),
      body: Container(
        padding: Layout.getSafePadding(horizontal: 4, vertical: 2),
        child: Layout.safeColumn(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          spacing: Layout.h(4),
          children: [
            Layout.safeText(
              'أمثلة على استخدام نظام التنبيهات',
              style: const AppTypography(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
            ),
            Layout.safeText(
              'اضغط على الأزرار أدناه لعرض أنواع مختلفة من التنبيهات',
              style: const AppTypography(
                fontSize: 16,
                color: AppColors.lightTextSecondary,
              ),
              maxLines: 3,
            ),
            _buildAlertButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertButtons() {
    return Layout.safeColumn(
      spacing: Layout.h(2),
      children: [
        // تنبيه نجاح
        Layout.safeButton(
          label: 'عرض تنبيه نجاح',
          onPressed: _showSuccessAlert,
          backgroundColor: AppColors.success,
          foregroundColor: AppColors.onPrimary,
        ),

        // تنبيه خطأ
        Layout.safeButton(
          label: 'عرض تنبيه خطأ',
          onPressed: _showErrorAlert,
          backgroundColor: AppColors.error,
          foregroundColor: AppColors.onPrimary,
        ),

        // تنبيه تحذير
        Layout.safeButton(
          label: 'عرض تنبيه تحذير',
          onPressed: _showWarningAlert,
          backgroundColor: AppColors.warning,
          foregroundColor: AppColors.onPrimary,
        ),

        // تنبيه معلومات
        Layout.safeButton(
          label: 'عرض تنبيه معلومات',
          onPressed: _showInfoAlert,
          backgroundColor: AppColors.info,
          foregroundColor: AppColors.onPrimary,
        ),

        // تنبيه تحميل
        Layout.safeButton(
          label: 'عرض تنبيه تحميل',
          onPressed: _showLoadingAlert,
          backgroundColor: AppColors.lightTextSecondary,
          foregroundColor: AppColors.onPrimary,
        ),

        // تنبيه مع أزرار متعددة
        Layout.safeButton(
          label: 'عرض تنبيه مع أزرار متعددة',
          onPressed: _showMultiButtonAlert,
          backgroundColor: AppColors.accent,
          foregroundColor: AppColors.onPrimary,
        ),
      ],
    );
  }

  // عرض تنبيه نجاح
  void _showSuccessAlert() {
    AppAlert.success(
      context: context,
      message: 'تم حفظ البيانات بنجاح',
    );
  }

  // عرض تنبيه خطأ
  void _showErrorAlert() {
    AppAlert.error(
      context: context,
      message: 'حدث خطأ أثناء حفظ البيانات',
      buttons: [
        AlertButton.retry(
          onPressed: () {
            AppAlert.dismiss(context);
            // محاولة إعادة الحفظ
          },
        ),
        AlertButton.cancel(),
      ],
    );
  }

  // عرض تنبيه تحذير
  void _showWarningAlert() {
    AppAlert.warning(
      context: context,
      message:
          'هل أنت متأكد من حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.',
      buttons: [
        AlertButton(
          text: 'حذف',
          color: AppColors.lightTextSecondary,
          onPressed: () {
            AppAlert.dismiss(context);
            // تنفيذ عملية الحذف
            _showSuccessAlert(); // عرض تنبيه نجاح بعد الحذف
          },
        ),
        AlertButton.cancel(),
      ],
    );
  }

  // عرض تنبيه معلومات
  void _showInfoAlert() {
    AppAlert.info(
      context: context,
      message: 'يمكنك السحب لليمين لعرض المزيد من الخيارات.',
      duration: const Duration(seconds: 5),
    );
  }

  // عرض تنبيه تحميل
  void _showLoadingAlert() async {
    AppAlert.loading(
      context: context,
      message: 'جاري تحميل البيانات...',
    );

    // محاكاة عملية تحميل
    await Future.delayed(const Duration(seconds: 3));

    // إغلاق تنبيه التحميل
    if (mounted) {
      AppAlert.dismiss(context);

      // عرض تنبيه نجاح بعد التحميل
      AppAlert.success(
        context: context,
        message: 'تم تحميل البيانات بنجاح',
      );
    }
  }

  // عرض تنبيه مع أزرار متعددة
  void _showMultiButtonAlert() {
    AppAlert.show(
      context: context,
      title: 'خيارات متعددة',
      message: 'يرجى اختيار أحد الخيارات التالية:',
      type: AlertType.info,
      buttons: [
        AlertButton(
          text: 'خيار 1',
          color: AppColors.lightTextSecondary,
          onPressed: () {
            AppAlert.dismiss(context);
            // تنفيذ الخيار 1
          },
        ),
        AlertButton(
          text: 'خيار 2',
          color: AppColors.lightTextSecondary,
          onPressed: () {
            AppAlert.dismiss(context);
            // تنفيذ الخيار 2
          },
        ),
        AlertButton(
          text: 'خيار 3',
          color: AppColors.lightTextSecondary,
          onPressed: () {
            AppAlert.dismiss(context);
            // تنفيذ الخيار 3
          },
        ),
        AlertButton.cancel(),
      ],
    );
  }
}
