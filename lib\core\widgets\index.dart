/// نظام الودجات الأساسية
/// يجمع جميع الودجات الأساسية في مكان واحد

// ========== الودجات الموحدة الجديدة (الأولوية) ==========
/// الودجات المشتركة الموحدة - استخدم هذه أولاً
export 'shared_widgets.dart';

// ========== الودجات المتخصصة ==========
/// ودجات متخصصة ومتقدمة
export 'adaptive_card.dart';
//export 'enhanced_ui_components.dart';
export 'data_table_widget.dart';
export 'stat_cards.dart';
export 'list_views.dart';
export 'app_drawer.dart';
export 'app_alert.dart';
export 'date_range_picker.dart';
export 'dialog_forms.dart';
export 'adaptive_form_fields.dart';
export 'form_field_examples.dart';
export 'safe_layout.dart';
export 'responsive_app.dart';
export 'lazy_provider_wrapper.dart';

// ========== ودجات للتوافق مع الإصدارات القديمة ==========
/// هذه الودجات متاحة للتوافق مع الكود القديم
/// يُنصح بالانتقال إلى الودجات الموحدة في shared_widgets.dart
export 'date_picker_field_compat.dart';
export 'dropdown_field.dart'; // سيتم توحيده لاحقاً
//export 'date_picker_field.dart'; // سيتم توحيده لاحقاً

// ========== ودجات مهملة (سيتم حذفها لاحقاً) ==========
/// ⚠️ هذه الودجات مكررة ومهملة - لا تستخدمها في كود جديد
/// استخدم الودجات الموحدة في shared_widgets.dart بدلاً منها

// المهملة - استخدم الودجات الموحدة بدلاً منها:
// export 'app_widgets.dart'; // مهمل - استخدم shared_widgets.dart
// export 'common_widgets.dart'; // مهمل - استخدم shared_widgets.dart  
// export 'custom_button.dart'; // مهمل - استخدم UnifiedButton
// export 'custom_text_field.dart'; // مهمل - استخدم UnifiedTextField
// export 'loading_indicator.dart'; // مهمل - استخدم UnifiedLoadingIndicator
// export 'confirmation_dialog.dart'; // مهمل - استخدم UnifiedConfirmationDialog
// export 'empty_state.dart'; // مهمل - استخدم UnifiedEmptyState
// export 'error_state.dart'; // مهمل - استخدم UnifiedErrorState
// export 'form_fields.dart'; // مهمل - استخدم UnifiedTextField
// export 'search_field.dart'; // مهمل - استخدم UnifiedTextField.search
// export 'password_field.dart'; // مهمل - استخدم UnifiedTextField.password
// export 'app_bar_widget.dart'; // مهمل - استخدم UnifiedAppBar
// export 'loading_overlay.dart'; // مهمل - استخدم UnifiedLoadingOverlay
// export 'action_buttons.dart'; // مهمل - استخدم UnifiedButton
// export 'action_button.dart'; // مهمل - استخدم UnifiedButton

/// 📋 دليل الاستخدام:
/// 
/// للودجات الجديدة، استخدم:
/// - UnifiedAppBar بدلاً من CustomAppBar أو AppBarWidget
/// - UnifiedButton بدلاً من CustomButton أو ActionButtons
/// - UnifiedTextField بدلاً من CustomTextField أو TextInputField
/// - UnifiedLoadingIndicator بدلاً من LoadingIndicator
/// - UnifiedLoadingOverlay بدلاً من LoadingOverlay
/// - UnifiedConfirmationDialog بدلاً من ConfirmationDialog
/// - UnifiedEmptyState بدلاً من EmptyState أو EmptyStateMessage
/// - UnifiedErrorState بدلاً من ErrorState
/// 
/// مثال:
/// ```dart
/// import '../core/widgets/index.dart';
/// 
/// // بدلاً من CustomAppBar
/// UnifiedAppBar(title: 'العنوان')
/// 
/// // بدلاً من CustomButton
/// UnifiedButton(text: 'حفظ', onPressed: () {})
/// 
/// // بدلاً من CustomTextField
/// UnifiedTextField(label: 'الاسم', controller: controller)
/// ```
